package com.ailk.newchnl.web;

import com.ailk.newchnl.constant.ChannelConstants;
import com.ailk.newchnl.entity.*;
import com.ailk.newchnl.entity.report.AdjustFeeDtl;
import com.ailk.newchnl.entity.xt.BusiThirdXXAssess;
import com.ailk.newchnl.mybatis.pagination.PageData;
import com.ailk.newchnl.mybatis.pagination.PageParameter;
import com.ailk.newchnl.service.AgentServiceFeeService;
import com.ailk.newchnl.service.AgentrRtaSjmcDyjlService;
import com.ailk.newchnl.service.MidHighEndRetentionAssessmentService;
import com.ailk.newchnl.service.PenaltyCoefficientsService;
import com.ailk.newchnl.service.channelPeopleAccountingDtlService;
import com.ailk.newchnl.util.*;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.*;

/**
 * Created by admin on 2015/5/5.
 */
@Controller
@RequestMapping("/agentAward")
public class AgentAwardController {
    private static final Logger logger = LoggerFactory.getLogger(AgentAwardController.class);
    @Resource
    private AgentServiceFeeService agentServiceFeeService;

    @Resource
    private PenaltyCoefficientsService penaltyCoefficientsService;

    @Resource
    private channelPeopleAccountingDtlService channelPeopleAccountingDtlService;

    @Resource
    private  AgentrRtaSjmcDyjlService agentrRtaSjmcDyjlService;

    @Resource
    private MidHighEndRetentionAssessmentService midHighEndRetentionAssessmentService;


    /**
     * @return
     * <AUTHOR>
     * 默认加载页面   合作厅服务费录入
     */
    @RequestMapping(value = "/page/serviceFeeAdd")
    public String serviceFeeAddPage() {
        return "award/serviceFeeAdd";
    }



    /**
     * @return
     * <AUTHOR>
     * 默认加载页面   在线公司扣罚系数录入
     */
    @RequestMapping(value = "/page/penaltyCoefficientsAdd")
    public String penaltyCoefficientsAdd() {
        return "award/penaltyCoefficientsAdd";
    }

    /**
     * @return
     * <AUTHOR>
     * 默认加载页面   在线公司考核得分录入
     */
    @RequestMapping(value = "/page/companyAssessmentScoresAdd")
    public String companyAssessmentScoresAdd() {
        return "award/companyAssessmentScoresAdd";
    }

    /**
     * @return
     * <AUTHOR>
     * 默认加载页面   铁通看管项目考核打分录入
     */
    @RequestMapping(value = "/page/tietongItemScoresAdd")
    public String tietongItemScoresAdd(HttpServletRequest request, HttpServletResponse response) {
        return "/award/tietongItemScoresAdd";
    }

    /**
     * @return
     * <AUTHOR>
     * 默认加载页面   业务集中稽核考核录入
     */
    @RequestMapping(value = "/page/businessAuditAssessAdd")
    public String businessAuditAssessAdd(HttpServletRequest request, HttpServletResponse response) {
        return "/award/businessAuditAssessAdd";
    }

    /**
     * @return
     * <AUTHOR>
     * 默认加载页面   业务集中稽核考核评分查询
     */
    @RequestMapping(value = "/page/businessAuditAssessQuery")
    public String businessAuditAssessQuery(HttpServletRequest request, HttpServletResponse response) {
        return "/award/businessAuditAssessQuery";
    }

    /**
     * @return
     * <AUTHOR>
     * 默认加载页面   在线公司专席结算录入
     */
    @RequestMapping(value = "/page/companySpecSettlementAdd")
    public String companySpecSettlementAdd(HttpServletRequest request, HttpServletResponse response) {
        return "/award/companySpecSettlementAdd";
    }


    /**
     * @return
     * <AUTHOR>
     * 默认加载页面   融合业务零产能
     */
    @RequestMapping(value = "/page/busiFuseProAdd")
    public String busiFuseProAdd(HttpServletRequest request, HttpServletResponse response) {
        return "/award/busiFuseProAdd";
    }

    /**
     * @return
     * <AUTHOR>
     * 默认加载页面   融合业务零产能管理
     */
    @RequestMapping(value = "/page/busiFuseProManage")
    public String busiFuseProManage(HttpServletRequest request, HttpServletResponse response) {
        return "/award/busiFuseProManage";
    }
    /**
     * <AUTHOR>
     * 中移铁通在线导购项目考核打分录入
     */
    @RequestMapping(value = "/page/tietongOnlineEval")
    public String tietongOnlineShoppingGuideEvaluation(HttpServletRequest request, HttpServletResponse response) {
        return "/award/tietongOnlineEval";
    }


    /**
     * @return
     * <AUTHOR>
     * 默认加载页面   社会渠道人员补贴核算
     */
    @RequestMapping(value = "/page/channelPeopleAccounting")
    public String channelPeopleAccounting(HttpServletRequest request, HttpServletResponse response) {
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        try {
            if (sPrivData == null) {
                logger.info("合作方查询，登陆超时");
                return null;
            }
        }catch (Exception e){
                logger.error("登陆超时",e);
        }
        return "/award/channelPeopleAccounting";
    }

    /**
     * @return
     * <AUTHOR>
     * 手机卖场店员激励签收数据统计结果导出
     */
    @RequestMapping("/mobileStoreClerksDateOfJLQS")
    @ResponseBody
    public Map<String, Object> mobileStoreClerkExport(HttpServletRequest request, HttpServletResponse response,
                                                 @RequestParam(value = "fileName", required = true) String fileName,
                                                 @RequestParam MultipartFile file) {
        Map<String, Object> returnMap = new HashMap<String, Object>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if (sPrivData == null) {
                logger.info("合作方查询，登陆超时");
                returnMap.put("msg", "登陆超时，请重新登入!");
                returnMap.put("status", "0");
            }
        }catch (Exception e) {
        logger.error("AgentAwardController。importAgentInfoFile error ", e);
        returnMap.put("status", 0);
        returnMap.put("msg", "文件导入失败，原因:" + e.getMessage());
    }
        return returnMap;
}


    /**
     * @return
     * <AUTHOR>
     * CRM工号信息及业务量统计需求结果导入
     */
    @RequestMapping("/subsidyImport")
    @ResponseBody
    public Map<String, Object> subsidyFileImport(HttpServletRequest request, HttpServletResponse response,
                                                 @RequestParam(value = "fileName", required = true) String fileName,
                                                 @RequestParam MultipartFile file) {
        Map<String, Object> returnMap = new HashMap<String, Object>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if (sPrivData == null) {
                logger.info("合作方查询，登陆超时");
                returnMap.put("msg", "登陆超时，请重新登入!");
                returnMap.put("status", "0");
            }
            fileName = java.net.URLDecoder.decode(URLDecoder.decode(fileName, "UTF-8"), "UTF-8");
            if (StringUtils.isBlank(fileName)) {
                returnMap.put("msg", "请选择要上传的文件!");
                returnMap.put("status", "0");
                return returnMap;
            } else if (!"XLS".equals(fileName.substring(fileName.length() - 3, fileName.length()).toUpperCase())) {
                returnMap.put("msg", "请选择Excel文件!");
                returnMap.put("status", "0");
                return returnMap;
            }
            try {
                String strFileName = request.getRealPath("/temp/agent/") + file.getOriginalFilename();
                File tempFile = new File(strFileName);
                file.transferTo(tempFile);

                List<ChannelSysBaseType> channelSysBaseTypeList =
                        ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 200, null, null);
                String ip = channelSysBaseTypeList.get(0).getCodeName();
                //服务器用户名
                channelSysBaseTypeList =
                        ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 201, null, null);
                String username = channelSysBaseTypeList.get(0).getCodeName();
                //服务器密码
                channelSysBaseTypeList =
                        ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 202, null, null);
                String password = channelSysBaseTypeList.get(0).getCodeName();

                //基本目录下的子目录
                channelSysBaseTypeList =
                        ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 203, null, null);
                String remoteFilePath = channelSysBaseTypeList.get(0).getCodeName();
                String remoteFileName = "subsidyAccountingFile_"+"_"+DateUtil.getCurrentDateFormat()+".xls";
                String remotePath =remoteFilePath+remoteFileName;
                SecureFileTransferProtocol.upload(ip, username, password, strFileName, remotePath);
                tempFile.delete();
                SubsidyAccountingFile subsidyAccountingFile = new SubsidyAccountingFile();
                subsidyAccountingFile.setFilePath(remotePath);
                subsidyAccountingFile.setFileName(remoteFileName);
                subsidyAccountingFile.setStatus(new Long(0));
                channelPeopleAccountingDtlService.saveSubsidyFileInfo(subsidyAccountingFile, sPrivData);
                returnMap.put("status", 1);
                returnMap.put("msg", "文件导入成功,稍后请点击下方查询按钮查看处理结果.");
                //发送4A增删改操作日志
                Boolean isSend4AMenu = false;
                List<ChannelSysBaseType> channelSysBaseTypeList1 = ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(50036, 7, null, null);
                for (ChannelSysBaseType channelSysBaseType : channelSysBaseTypeList1) {
                    if (channelSysBaseType.getCodeName() != null && channelSysBaseType.getCodeName().equals("1")) {
                        isSend4AMenu = true;
                    }
                }
                if (isSend4AMenu) {
                    logger.info("向4A发送渠道系统增删改操作日志");
                    Channel4AMenu.sendMenuLog(request, 2, "社会渠道人员补贴核算导入", "", "社会渠道人员补贴核算导入");
                }
            } catch (Exception e) {
                logger.error("EntityBatchSyncFileController。importAgentInfoFile error ", e);
                returnMap.put("status", 0);
                returnMap.put("msg", "文件导入失败，原因:" + e.getMessage());
            }
        } catch (Exception e) {
            logger.error("AgentAwardController。importAgentInfoFile error ", e);
            returnMap.put("status", 0);
            returnMap.put("msg", "文件导入失败，原因:" + e.getMessage());
        }
        return returnMap;
    }

    /**
     * @return
     * <AUTHOR>
     * 社会渠道人员补贴核算数据导出
     */
    @RequestMapping("/subsidyExport")
    public void subsidyExport(HttpServletRequest request, HttpServletResponse response, @RequestParam("CLIENT_CIPHER") String clientCipher,
                              @RequestParam("data") String data, PageParameter page) {

        Map<String, Object> repMap = new HashMap<String, Object>();

        String billMonth = DateUtil.formatDate(DateUtil.getCurrDate(), "yyyyMM");
        response.setHeader("Cache-Control", "no-cache");
        response.setCharacterEncoding("UTF-8");
        response.addHeader("expires", "0");
        response.reset();
        response.resetBuffer();
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        Long fileSeq=null;
        String begDate = null;
        String endDate = null;
        try {
            if (sPrivData == null) {
                throw new Exception("登录信息不存在！");
            }
            try {
                if(com.ailk.newchnl.util.StringUtils.isNullOrBlank(clientCipher)||!"newchannel".equals(clientCipher)){
                    throw new Exception("参数不合法,请不要非法访问！");
                }
                clientCipher = Base64.getEncoder().encodeToString("ASSDFGHA$newchnl".getBytes()).substring(0, 16);
                if (com.ailk.newchnl.util.StringUtils.isNotNullOrBlank(data)) {
                    String jsonData = AES.Decrypts(data, clientCipher);
                    String[] strParams = jsonData.split("&");
                    Map paramMap = new HashMap();
                    for (String strParam : strParams) {
                        String[] key_value = strParam.split("=");
                        paramMap.put(key_value[0].trim(), key_value[1]);
                    }
                    fileSeq = MapUtils.getLong(paramMap, "fileSeq");
                    begDate = MapUtils.getString(paramMap, "begDate");
                    endDate = MapUtils.getString(paramMap, "endDate");
                    begDate=begDate.replace("-","");
                    endDate=endDate.replace("-","");
                    begDate = begDate.substring(0,6);
                    endDate = endDate.substring(0,6);
                }
            }catch (Exception e1){
                throw new Exception("参数不合法,请不要非法访问！");
            }
            OutputStream outputStream = response.getOutputStream();
            Map<String, String> titleField = getTitleField1();
            page.setPageUsed(0);
            //直供网点
            PageData<peopleAccountingResult> pageData0 = channelPeopleAccountingDtlService.getPeopleAccountingResult(begDate, endDate,  "手机专卖店", "授权代理店", fileSeq, page);
            List<Map<String, Object>> rows0 = new LinkedList<Map<String, Object>>();
            for (peopleAccountingResult entity : pageData0.getRows()) {
                rows0.add(objToMap(entity));
            }
            //手机卖场
            PageData<peopleAccountingResult> pageData1 = channelPeopleAccountingDtlService.getPeopleAccountingResult1(begDate, endDate, "卖场", fileSeq, page);
            List<Map<String, Object>> rows1 = new LinkedList<Map<String, Object>>();
            for (peopleAccountingResult entity : pageData1.getRows()) {
                rows1.add(objToMap(entity));
            }
            //社会加盟店
            PageData<peopleAccountingResult> pageData2 = channelPeopleAccountingDtlService.getPeopleAccountingResult1(begDate, endDate, "社会加盟店", fileSeq, page);
            List<Map<String, Object>> rows2 = new LinkedList<Map<String, Object>>();
            for (peopleAccountingResult entity : pageData2.getRows()) {
                rows2.add(objToMap(entity));
            }
            PageData<peopleAccountingResult> pageData6 = channelPeopleAccountingDtlService.getPeopleAccountingResult1(begDate, endDate, "合作店", fileSeq, page);
            List<Map<String, Object>> rows6 = new LinkedList<Map<String, Object>>();
            for (peopleAccountingResult entity : pageData6.getRows()) {
                rows6.add(objToMap(entity));
            }
            //泛渠道
            PageData<peopleAccountingResult> pageData3 = channelPeopleAccountingDtlService.getPeopleAccountingResult1(begDate, endDate, "泛渠道", fileSeq, page);
            List<Map<String, Object>> rows3 = new LinkedList<Map<String, Object>>();
            for (peopleAccountingResult entity : pageData3.getRows()) {
                rows3.add(objToMap(entity));
            }
            //电子渠道
            PageData<peopleAccountingResult> pageData4 = channelPeopleAccountingDtlService.getPeopleAccountingResult1(begDate, endDate, "电子渠道", fileSeq, page);
            List<Map<String, Object>> rows4 = new LinkedList<Map<String, Object>>();
            for (peopleAccountingResult entity : pageData4.getRows()) {
                rows4.add(objToMap(entity));
            }

            //随销渠道
            PageData<peopleAccountingResult> pageData5 = channelPeopleAccountingDtlService.getPeopleAccountingResult1(begDate, endDate, "随销渠道", fileSeq, page);
            List<Map<String, Object>> rows5 = new LinkedList<Map<String, Object>>();
            for (peopleAccountingResult entity : pageData5.getRows()) {
                rows5.add(objToMap(entity));
            }
            List<List<Map<String, Object>>> header = new ArrayList<List<Map<String, Object>>>();
            List<Map<String, Object>> header_ = new ArrayList<Map<String, Object>>();
            String str[][] = {
                    {"", "1"},
                    {"", "1"},
                    {"", "1"},
                    {"", "1"},
                    {"", "1"},
                    {"人员补贴（系统取数）", "3"}};
            for (String str_[] : str) {
                Map<String, Object> map_ = new HashMap<String, Object>();
                map_.put("title", str_[0]);
                map_.put("count", str_[1]);
                header_.add(map_);
            }
            header.add(header_);
            XSSFWorkbook workbook = new XSSFWorkbook();
            ExcelUtil.exportExcelForDataGridHasMoreHeader1("手机专卖店+授权代理店", workbook, 0, formatDataMap(1), header, columns(titleField), rows0);
            ExcelUtil.exportExcelForDataGridHasMoreHeader1("手机卖场", workbook, 1, formatDataMap(1), header, columns(titleField), rows1);
            ExcelUtil.exportExcelForDataGridHasMoreHeader1("社会加盟店", workbook, 2, formatDataMap(1), header, columns(titleField), rows2);
            ExcelUtil.exportExcelForDataGridHasMoreHeader1("泛渠道", workbook, 3, formatDataMap(1), header, columns(titleField), rows3);
            ExcelUtil.exportExcelForDataGridHasMoreHeader1("电子渠道", workbook, 4, formatDataMap(1), header, columns(titleField), rows4);
            ExcelUtil.exportExcelForDataGridHasMoreHeader1("随销渠道", workbook, 5, formatDataMap(1), header, columns(titleField), rows5);
            ExcelUtil.exportExcelForDataGridHasMoreHeader1("合作店", workbook, 6, formatDataMap(1), header, columns(titleField), rows6);
            workbook.write(outputStream);
            outputStream.close();
            //发送4A增删改操作日志
            Boolean isSend4AMenu = false;
            List<ChannelSysBaseType> channelSysBaseTypeList1 = ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(50036, 7, null, null);
            for (ChannelSysBaseType channelSysBaseType : channelSysBaseTypeList1) {
                if (channelSysBaseType.getCodeName() != null && channelSysBaseType.getCodeName().equals("1")) {
                    isSend4AMenu = true;
                }
            }
            if (isSend4AMenu) {
                logger.info("向4A发送渠道系统增删改操作日志");
                Channel4AMenu.sendMenuLog(request, 2, "社会渠道人员补贴核算导出", "", "社会渠道人员补贴核算导出");
            }
        } catch (Exception e) {
            logger.error("导出核算结束信息失败", e);
            repMap.put("msg", "导出核算结束信息失败");
            response.setContentType("text/html;charset=UTF-8");
            response.setCharacterEncoding("UTF-8");
            try {
                response.getWriter().write(e.getMessage());
            }catch (Exception e1){
                logger.error("错误信息：",e1);
            }
        }
    }


    /**
     * @return
     * <AUTHOR>
     * 手机卖场店员激励签收数据统计报表导出
     */
    @RequestMapping("/exportSJMC")
    public void exportSJMC(HttpServletRequest request, HttpServletResponse response, @RequestParam("CLIENT_CIPHER") String clientCipher,
                           @RequestParam("data") String data, PageParameter page) {
        Map<String, Object> repMap = new HashMap<String, Object>();

        response.setHeader("Cache-Control", "no-cache");
        response.setCharacterEncoding("UTF-8");
        response.addHeader("expires", "0");
        response.reset();
        response.resetBuffer();
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        String operatorsName=null;
        String channelName=null;
        String startTime=null;
        String endTime=null;

        try {
            if (sPrivData == null) {
                throw new Exception("登录信息不存在！");
            }
            try {
                if(com.ailk.newchnl.util.StringUtils.isNullOrBlank(clientCipher)||!"newchannel".equals(clientCipher)){
                    throw new Exception("参数不合法,请不要非法访问！");
                }
                clientCipher = Base64.getEncoder().encodeToString("ASSDFGHA$newchnl".getBytes()).substring(0, 16);
                if (com.ailk.newchnl.util.StringUtils.isNotNullOrBlank(data)) {
                    String jsonData = AES.Decrypts(data, clientCipher);
                    String[] strParams = jsonData.split("&");
                    Map paramMap = new HashMap();
                    for (String strParam : strParams) {
                        String[] key_value = strParam.split("=");
                        if (key_value.length==2){
                            paramMap.put(key_value[0].trim(), key_value[1]);
                        }else {
                            paramMap.put(key_value[0].trim(), null);
                        }

                    }
                    operatorsName = MapUtils.getString(paramMap, "operatorsName");
                    channelName = MapUtils.getString(paramMap, "channelName");
                    startTime = MapUtils.getString(paramMap, "startTime");
                    endTime = MapUtils.getString(paramMap, "endTime");

                    OutputStream outputStream = response.getOutputStream();
                    Map<String, String> titleField = getTitleField2();
                    page.setPageUsed(0);
                    PageData<AgentrRtaSjmcDyjl> pageData = agentrRtaSjmcDyjlService.agentrRtaSjmcDyjl(operatorsName, channelName ,startTime ,endTime,page);
                    List<Map<String, Object>> rows0 = new LinkedList<Map<String, Object>>();

                    for (AgentrRtaSjmcDyjl entity : pageData.getRows()) {
                        rows0.add(objToMap(entity));
                    }

                    List<List<Map<String, Object>>> header = new ArrayList<List<Map<String, Object>>>();
                    List<Map<String, Object>> header_ = new ArrayList<Map<String, Object>>();
                    String str[][] = {
                            {"", "1"},
                            {"", "1"},
                            {"", "1"},
                            {"", "1"},
                            {"", "1"},
                            {"", "1"},
                            {"", "1"}};
                    for (String str_[] : str) {
                        Map<String, Object> map_ = new HashMap<String, Object>();
                        map_.put("title", str_[0]);
                        map_.put("count", str_[1]);
                        header_.add(map_);
                    }
                    header.add(header_);
                    XSSFWorkbook workbook = new XSSFWorkbook();
                    ExcelUtil.exportExcelForDataGridHasMoreHeader("1", "手机卖场店员激励签收数据统计报表", formatDataMap1(6), header, columns(titleField), rows0, outputStream);
                    workbook.write(outputStream);
                    outputStream.close();
                }
            }catch (Exception e1){
                throw new Exception("参数不合法,请不要非法访问！");
            }
        } catch (Exception e) {
            logger.error("手机卖场店员激励签收数据统计报表导出失败", e);
        }
    }

    private static Map<String, Map<String, String>> formatDataMap1(Integer formatData) {
        Map<String, Map<String, String>> map = new HashMap<String, Map<String, String>>();
        Map<String, Integer> fieldValues = new HashMap<String, Integer>();

        fieldValues.put("state", 57671);
        for (Map.Entry<String, Integer> fieldValue : fieldValues.entrySet()) {
            List<ChannelSysBaseType> types = ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(fieldValue.getValue(), null, null, null);
            Map<String, String> typeMap = new HashMap<String, String>();
            for (ChannelSysBaseType type : types) {
                typeMap.put(type.getCodeId().toString(), type.getCodeName());
            }
            map.put(fieldValue.getKey(), typeMap);
        }
        return map;
    }
    /**
     * 查询上传社会渠道人员补贴核算的基础信息
     *
     * @param request
     * @param response
     * @param strBegDate 开始日期
     * @param strEndDate 结束日期
     * @return 返回查询数据集
     * @throws Exception 抛出所有异常
     */
    @RequestMapping("/getSubsidyAccountingFileList")
    @ResponseBody
    public PageData<SubsidyAccountingFile> getSubsidyAccountingFileList(HttpServletRequest request, HttpServletResponse response,
                                                                        @RequestParam(value = "strBegDate", required = true) Date strBegDate,
                                                                        @RequestParam(value = "strEndDate", required = true) Date strEndDate, PageParameter page) throws Exception {
        PageData<SubsidyAccountingFile> pageDate = null;
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if (sPrivData == null) {
                logger.info("合作方查询，登陆超时");
                return null;
            }
            pageDate = channelPeopleAccountingDtlService.getSubsidyAccountingFileList(strBegDate, strEndDate, page);
        } catch (Exception e) {
            logger.error("[SubsidyAccountingFile]查询批量导入进度失败 !", e);

        }
        return pageDate;
    }

    private static Map<String, String> getTitleField1() {
        Map<String, String> map = new LinkedHashMap<String, String>();
        List<String> fields = new ArrayList<String>();
        List<String> titles = new ArrayList<String>();
        String[][] column = {{"", "核算月", "billMonth"},
                {"", "归属单位", "districtId"},
                {"", "渠道类型", "channelType"},
                {"", "合作方名称", "channelEntityName"},
                {"", "网点名称", "nodeName"},
                {"人员补贴（系统取数）", "活跃工号数", "activeJobNumber"},
                {"人员补贴（系统取数）", "补贴单价", "subsidyPrice"},
                {"人员补贴（系统取数）", "月核算金额", "monthAccountingAmount"}};
        for (String[] temp : column) {
            titles.add(temp[1]);
            fields.add(temp[2]);
        }
        for (int i = 0; i < fields.size(); i++) {
            map.put(fields.get(i), titles.get(i));
        }
        return map;
    }

    private static Map<String, String> getTitleField2() {
        Map<String, String> map = new LinkedHashMap<String, String>();
        List<String> fields = new ArrayList<String>();
        List<String> titles = new ArrayList<String>();
        String[][] column = {{"", "代理商", "operatorsName"},
                {"", "网点", "channelName"},
                {"", "工号", "operatorsCode"},
                {"", "店员激励日期", "startMonth"},
                {"", "当日店员激励金额（元）", "charge"},
                {"", "激励签收日期", "signDate"},
                {"", "激励签收情况", "state"}};
        for (String[] temp : column) {
            titles.add(temp[1]);
            fields.add(temp[2]);
        }
        for (int i = 0; i < fields.size(); i++) {
            map.put(fields.get(i), titles.get(i));
        }
        return map;
    }


    /**
     * @return
     * <AUTHOR>
     * 在线公司考核得分录入界面上传附件(经分报表/考核表)
     */
    @RequestMapping("/filesUpload")
    @ResponseBody
    public Map<String, Object> filesUpload(HttpServletRequest request, HttpServletResponse response,
                                           @RequestParam(value = "billMonth", required = true) String billMonth,
                                           @RequestParam(value = "fileName", required = true) String fileName,
                                           @RequestParam MultipartFile[] files) {

        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            for (MultipartFile multipartFile : files) {
                if (!multipartFile.isEmpty()) {
                    //服务器IP
                    List<ChannelSysBaseType> channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 0, null, null);
                    String ip = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器用户名
                    channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 1, null, null);
                    String username = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器密码
                    channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 2, null, null);
                    String password = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器文件路径
                    channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 111, null, null);
                    String remotePath = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器文件
                    String remoteFileName = multipartFile.getOriginalFilename();
                    String remoteFile = remotePath + billMonth + "_" + UUID.randomUUID().toString();
                    SecureFileTransferProtocol.upload(ip, username, password, multipartFile.getInputStream(), remoteFile);
                    resultMap.put("remoteFileName", remoteFileName);
                    resultMap.put("remotePath", remoteFile);
                }
            }
            resultMap.put("status", "1");
            resultMap.put("msg", "文件上传成功！");
        } catch (Exception e) {
            resultMap.put("status", "0");
            resultMap.put("msg", "文件上传失败！");
            logger.error("文件上传失败！", e);
        }
        return resultMap;
    }

    /**
     * @Description 校验当月的在线公司考核得分是否已经录入
     * @Params [request, response, channelEntityName, billMonth, baseScore, incentiveScore, settlementCoefficient, remoteFileName, remotePath]
     * @Return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @CreateDate 2020/3/11
     */
    @RequestMapping("/checkScoreIsExist")
    @ResponseBody
    public Map<String, Object> checkScoreIsExist(HttpServletRequest request, HttpServletResponse response,
                                                 @RequestParam(value = "channelEntityName", required = true) String channelEntityName,
                                                 @RequestParam(value = "billMonth", required = true) String billMonth) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            channelEntityName = URLDecoder.decode(URLDecoder.decode(channelEntityName, "UTF-8"), "UTF-8");
            CompanyAssessmentScore companyAssessmentScore = new CompanyAssessmentScore();
            companyAssessmentScore.setBillMonth(billMonth);
            Boolean flag = agentServiceFeeService.checkScoreIsExist(channelEntityName, companyAssessmentScore);
            // 已经存在对应数据，现在开始将ftp上的对应文件删除
            if (flag == true) {
                //服务器IP
                List<ChannelSysBaseType> channelSysBaseTypeList =
                        ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 0, null, null);
                String ip = channelSysBaseTypeList.get(0).getCodeName();
                //服务器用户名
                channelSysBaseTypeList =
                        ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 1, null, null);
                String username = channelSysBaseTypeList.get(0).getCodeName();
                //服务器密码
                channelSysBaseTypeList =
                        ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 2, null, null);
                String password = channelSysBaseTypeList.get(0).getCodeName();
                String filePath=agentServiceFeeService.getRemoteFilePath(billMonth);
//                删除对应文件，这个文件上传之后就通过UUID.randomUUID()乱码处理了，选中这个路径就可以删除了
                    SecureFileTransferProtocol.deleteFile(ip, username, password, filePath);
//                将数据库中的对应数据的rec_status置为0
                agentServiceFeeService.resetRecStatus(billMonth);
                resultMap.put("status", "0");
            } else {
            // 录入的数据在库中不存在，直接进行下一步操作
                resultMap.put("status", "0");
            }
        } catch (Exception e) {
                logger.error("校验文件是否存在失败",e);
                resultMap.put("status", "1");
                resultMap.put("msg","校验文件是否存在失败");
        }
        return resultMap;
    }

    /**
     * @Description 在线公司考核得分录入--保存
     * @Params [request, response, channelEntityName, billMonth, baseScore, incentiveScore, settlementCoefficient, fileName, filePath]
     * @Return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @CreateDate 2020/3/12
     */
    @RequestMapping("/companyAssessmentScoreSave")
    @ResponseBody
    public Map<String, Object> companyAssessmentScoreSave(HttpServletRequest request, HttpServletResponse response,
                                                          @RequestParam(value = "channelEntityName", required = true) String channelEntityName,
                                                          @RequestParam(value = "billMonth", required = true) String billMonth,
                                                          @RequestParam(value = "baseScore", required = true) Integer baseScore,
                                                          @RequestParam(value = "incentiveScore", required = true) Integer incentiveScore,
                                                          @RequestParam(value = "settlementCoefficient", required = true) Double settlementCoefficient,
                                                          @RequestParam(value = "remoteFileName", required = true) String fileName,
                                                          @RequestParam(value = "remotePath", required = true) String filePath) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if (sPrivData == null) {
                resultMap.put("status", "0");
                resultMap.put("msg", "登陆超时，请重新登陆！");
                return resultMap;
            }
            channelEntityName = URLDecoder.decode(URLDecoder.decode(channelEntityName, "UTF-8"), "UTF-8");
            CompanyAssessmentScore companyAssessmentScore = new CompanyAssessmentScore();
            companyAssessmentScore.setBaseScore(baseScore);
            companyAssessmentScore.setBillMonth(billMonth);
            companyAssessmentScore.setIncentiveScore(incentiveScore);
            companyAssessmentScore.setSettlementCoefficient(settlementCoefficient);
            companyAssessmentScore.setFileName(fileName);
            companyAssessmentScore.setFilePath(filePath);
            companyAssessmentScore.setOpId(sPrivData.getOpId());
            companyAssessmentScore.setOrgId(sPrivData.getOrgId());
            companyAssessmentScore.setCreateDate(DateUtil.getCurrDate());
            companyAssessmentScore.setDoneDate(DateUtil.getCurrDate());
            companyAssessmentScore.setRecStatus(1);
            agentServiceFeeService.companyScoresSave(channelEntityName, companyAssessmentScore);
            resultMap.put("status", "1");
            resultMap.put("msg", "在线公司考核得分录入成功！");
        } catch (Exception e) {
            resultMap.put("status", "0");
            resultMap.put("msg", "在线公司考核得分录入失败！");
            logger.error("在线公司考核得分录入保存失败！！" + e);
        }
        return resultMap;
    }

    /**
     * @Description 在线公司考核得分录入--修改
     * @Params [request, response, channelEntityName, billMonth, baseScore, incentiveScore, settlementCoefficient, fileName, filePath]
     * @Return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @CreateDate 2020/3/12
     */
    @RequestMapping("/companyAssessmentScoreModify")
    @ResponseBody
    public Map<String, Object> companyAssessmentScoreModify(HttpServletRequest request, HttpServletResponse response,
                                                            @RequestParam(value = "channelEntityName", required = true) String channelEntityName,
                                                            @RequestParam(value = "billMonth", required = true) String billMonth,
                                                            @RequestParam(value = "baseScore", required = true) Integer baseScore,
                                                            @RequestParam(value = "incentiveScore", required = true) Integer incentiveScore,
                                                            @RequestParam(value = "settlementCoefficient", required = true) Double settlementCoefficient,
                                                            @RequestParam(value = "remoteFileName", required = true) String fileName,
                                                            @RequestParam(value = "remotePath", required = true) String filePath) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if (sPrivData == null) {
                resultMap.put("status", "0");
                resultMap.put("msg", "登陆超时，请重新登陆！");
                return resultMap;
            }
            channelEntityName = URLDecoder.decode(URLDecoder.decode(channelEntityName, "UTF-8"), "UTF-8");
            CompanyAssessmentScore companyAssessmentScore = new CompanyAssessmentScore();
            companyAssessmentScore.setBaseScore(baseScore);
            companyAssessmentScore.setBillMonth(billMonth);
            companyAssessmentScore.setIncentiveScore(incentiveScore);
            companyAssessmentScore.setSettlementCoefficient(settlementCoefficient);
            companyAssessmentScore.setFileName(fileName);
            companyAssessmentScore.setFilePath(filePath);
            companyAssessmentScore.setOpId(sPrivData.getOpId());
            companyAssessmentScore.setOrgId(sPrivData.getOrgId());
            companyAssessmentScore.setCreateDate(DateUtil.getCurrDate());
            companyAssessmentScore.setDoneDate(DateUtil.getCurrDate());
            companyAssessmentScore.setRecStatus(1);
            agentServiceFeeService.companyScoresSave(channelEntityName, companyAssessmentScore);
            resultMap.put("status", "1");
            resultMap.put("msg", "在线公司考核得分修改成功！");
        } catch (Exception e) {
            resultMap.put("status", "0");
            resultMap.put("msg", "在线公司考核得分修改失败！");
            logger.error("在线公司考核得分修改失败！！" + e);
        }
        return resultMap;
    }

    /**
     * @Description 查询在线公司考核得分信息
     * @Params [request, response, channelEntityName, billMonth, page]
     * @Return com.ailk.newchnl.mybatis.pagination.PageData<com.ailk.newchnl.entity.CompanyAssessmentScore>
     * <AUTHOR>
     * @CreateDate 2020/3/11
     */
    @RequestMapping("/getScoreInfo")
    @ResponseBody
    public PageData<CompanyAssessmentScore> getAssessmentScore(HttpServletRequest request, HttpServletResponse response,
                                                               @RequestParam(value = "channelEntityName", required = true) String channelEntityName,
                                                               @RequestParam(value = "billMonth", required = true) String billMonth,
                                                               PageParameter page) {
        PageData pageData = new PageData(null, 0);
        try {
            channelEntityName = URLDecoder.decode(URLDecoder.decode(channelEntityName, "UTF-8"), "UTF-8");
            CompanyAssessmentScore companyAssessmentScore = new CompanyAssessmentScore();
            companyAssessmentScore.setBillMonth(billMonth);
            pageData = agentServiceFeeService.queryAssessmentScore(companyAssessmentScore, page);
        } catch (Exception e) {
            logger.error("查询在线公司考核得分失败！", e);
        }
        return pageData;
    }

    @RequestMapping("/getScoreDetailInfo")
    @ResponseBody
    public List<CompanyAssessmentScore> getScoreDetailInfo(HttpServletRequest request, HttpServletResponse response,
                                                           @RequestParam(value = "channelEntityName", required = true) String channelEntityName,
                                                           @RequestParam(value = "billMonth", required = true) String billMonth) {
        List<CompanyAssessmentScore> companyAssessmentScoreList = null;
        try {
            channelEntityName = URLDecoder.decode(URLDecoder.decode(channelEntityName, "UTF-8"), "UTF-8");
            CompanyAssessmentScore companyAssessmentScore = new CompanyAssessmentScore();
            companyAssessmentScore.setBillMonth(billMonth);
            companyAssessmentScoreList = agentServiceFeeService.queryDetailScoreInfo(companyAssessmentScore);
        } catch (Exception e) {
            logger.error("查询在线公司考核得分失败！", e);
        }
        return companyAssessmentScoreList;

    }

    /**
     * 合作厅服务费录入
     *
     * @param request
     * @param response
     * @param channelEntityId
     * @param billMonth
     * @param countFee
     * @param totalFee
     * @param totalPoint
     * @param deductFee
     * @param notes
     * @return
     */
    @RequestMapping("/serviceFeeAdd")
    @ResponseBody
    public Map<String, String> serviceFeeAdd(HttpServletRequest request, HttpServletResponse response,
                                             @RequestParam(value = "channelEntityId", required = true) Long channelEntityId,
                                             @RequestParam(value = "billMonth", required = true) Long billMonth,
                                             @RequestParam(value = "countFee", required = false) Long countFee,
                                             @RequestParam(value = "totalFee", required = false) Long totalFee,
                                             @RequestParam(value = "totalPoint", required = false) Long totalPoint,
                                             @RequestParam(value = "deductFee", required = false) Long deductFee,
                                             @RequestParam(value = "notes", required = false) String notes) {
        Map<String, String> map = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            notes = java.net.URLDecoder.decode(URLDecoder.decode(notes, "UTF-8"), "UTF-8");
            map = agentServiceFeeService.agentServiceFeeSave(channelEntityId, billMonth, countFee, totalFee, totalPoint, deductFee, notes, sPrivData);
        } catch (Exception e) {
            logger.error("服务费录入失败", e);
            map.put("status", "1");
            map.put("msg", "服务费录入失败");
        }
        return map;
    }

    @RequestMapping(value = "/page/serviceFeeManager")
    public String serviceFeeManager() {
        return "award/serviceFeeManager";
    }

    /**
     * 服务费查询
     *
     * @param request
     * @param response
     * @param nodeId
     * @param billMonth
     * @param page
     * @return
     */
    @RequestMapping("/serviceFeeQuery")
    @ResponseBody
    public PageData<AgentServiceFeeDtl> serviceFeeQuery(HttpServletRequest request, HttpServletResponse response,
                                                        @RequestParam(value = "nodeId", required = true) Long nodeId,
                                                        @RequestParam(value = "billMonth", required = true) Long billMonth,
                                                        PageParameter page) {
        PageData<AgentServiceFeeDtl> pageData = new PageData<AgentServiceFeeDtl>(null, 0);
        try {
            pageData = agentServiceFeeService.serviceFeeQuery(nodeId, billMonth, page);
            if (pageData == null) {
                pageData = new PageData<AgentServiceFeeDtl>(null, 0);
            }
        } catch (Exception e) {
            logger.error("合作方欠费查询失败", e);
        }
        return pageData;
    }

    /**
     * 服务费修改
     *
     * @param request
     * @param response
     * @param nodeId
     * @param billMonth
     * @param countFee
     * @param totalFee
     * @param totalPoint
     * @param deductFee
     * @param notes
     * @return
     */
    @RequestMapping("/serviceFeeUpdate")
    @ResponseBody
    public Map<String, String> serviceFeeUpdate(HttpServletRequest request, HttpServletResponse response,
                                                @RequestParam(value = "nodeId", required = true) Long nodeId,
                                                @RequestParam(value = "billMonth", required = true) Long billMonth,
                                                @RequestParam(value = "countFee", required = false) Long countFee,
                                                @RequestParam(value = "totalFee", required = false) Long totalFee,
                                                @RequestParam(value = "totalPoint", required = false) Long totalPoint,
                                                @RequestParam(value = "deductFee", required = false) Long deductFee,
                                                @RequestParam(value = "notes", required = false) String notes) {
        Map<String, String> map = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            notes = java.net.URLDecoder.decode(URLDecoder.decode(notes, "UTF-8"), "UTF-8");
            map = agentServiceFeeService.agentServiceFeeUpdate(nodeId, billMonth, countFee, totalFee, totalPoint, deductFee, notes, sPrivData);
        } catch (Exception e) {
            logger.error("服务费修改失败", e);
            map.put("status", "1");
            map.put("msg", "服务费修改失败");
        }
        return map;
    }

    @RequestMapping("/serviceFeeDelete")
    @ResponseBody
    public Map<String, String> serviceFeeDelete(HttpServletRequest request, HttpServletResponse response,
                                                @RequestParam(value = "nodeId", required = true) Long nodeId,
                                                @RequestParam(value = "billMonth", required = true) Long billMonth) {
        Map<String, String> map = new HashMap<String, String>();
        try {
            map = agentServiceFeeService.serviceFeeDelete(nodeId, billMonth);
        } catch (Exception e) {
            logger.error("服务费修改失败", e);
            map.put("status", "1");
            map.put("msg", "服务费修改失败");
        }
        return map;
    }

    @RequestMapping(value = "/page/feeDefaultFeeAdd")
    public String feeAdjustAddPage() {
        return "award/feeDefaultFeeAdd";
    }
    /*
     *手机卖场店员激励签收数据统计报表
     */
    @RequestMapping(value = "/page/mobileStoreClerksDateOfJLQS")
    public String mobileStoreClerksDateOfJLQS() {
        return "award/mobileStoreClerksDateOfJLQS";
    }

    @RequestMapping(value = "/rtaSjmcDyjlInfoQuery")
    @ResponseBody
    public PageData<AgentrRtaSjmcDyjl> rtaSjmcDyjlInfoQuery(HttpServletRequest request, HttpServletResponse response,
                                                            @RequestParam(value = "operatorsName", required = false) String operatorsName,
                                                            @RequestParam(value = "channelName", required = false) String channelName,
                                                    @RequestParam(value = "startTime", required = false) String startTime,
                                                    @RequestParam(value = "endTime", required = false) String endTime,
                                                            PageParameter page) throws Exception {
        Map<String, String> map = new HashMap<String, String>();
        PageData<AgentrRtaSjmcDyjl> pageDate = null;

        try {
            pageDate = agentrRtaSjmcDyjlService.agentrRtaSjmcDyjl(operatorsName, channelName ,startTime ,endTime,page);
        } catch (Exception e) {
            logger.error("手机卖场店员激励签收数据查询失败", e);

        }
        return pageDate;
    }
    /**
     * 录入欠费前验证
     *
     * @param request
     * @param response
     * @param channelEntityId
     * @param accId
     * @param billMonth
     * @return
     */
    @RequestMapping("/findAgentFee")
    @ResponseBody
    public Map<String, String> findAgentFee(HttpServletRequest request, HttpServletResponse response,
                                            @RequestParam(value = "channelEntityId", required = true) Long channelEntityId,
                                            @RequestParam(value = "accId", required = true) Long accId,
                                            @RequestParam(value = "billMonth", required = true) Integer billMonth) {
        Map<String, String> map = new HashMap<String, String>();
        try {
            map = agentServiceFeeService.findAgentFee(channelEntityId, accId, billMonth);
        } catch (Exception e) {
            logger.error("录入欠费前查询失败", e);
            map.put("status", "1");
            map.put("msg", "录入欠费前查询失败");
        }
        return map;
    }

    /**
     * 欠费录入
     *
     * @param request
     * @param response
     * @param channelEntityId
     * @param accId
     * @param billMonth
     * @param totalFee
     * @param notes
     * @return
     */
    @RequestMapping("/feeDefaultFeeAdd")
    @ResponseBody
    public Map<String, String> feeDefaultFeeAdd(HttpServletRequest request, HttpServletResponse response,
                                                @RequestParam(value = "channelEntityId", required = true) Long channelEntityId,
                                                @RequestParam(value = "accId", required = true) Long accId,
                                                @RequestParam(value = "billMonth", required = true) Integer billMonth,
                                                @RequestParam(value = "totalFee", required = false) Long totalFee,
                                                @RequestParam(value = "notes", required = false) String notes) {
        Map<String, String> map = new HashMap<String, String>();
        try {
            map = agentServiceFeeService.feeDefaultFeeAdd(channelEntityId, billMonth, accId, totalFee, notes);
        } catch (Exception e) {
            logger.error("合作方欠费录入失败", e);
            map.put("status", "1");
            map.put("msg", "合作方欠费录入失败");
        }
        return map;
    }

    @RequestMapping(value = "/page/feeDefaultFeeQuery")
    public String feeAdjustAddQueryPage() {
        return "award/feeDefaultFeeQuery";
    }

    /**
     * 欠费查询
     *
     * @param request
     * @param response
     * @param accId
     * @param billMonth
     * @param page
     * @return
     */
    @RequestMapping("/feeDefaultFeeQuery")
    @ResponseBody
    public PageData<AdjustFeeDtl> feeAdjustAddQuery(HttpServletRequest request, HttpServletResponse response,
                                                    @RequestParam(value = "accId", required = true) Long accId,
                                                    @RequestParam(value = "billMonth", required = true) Integer billMonth,
                                                    PageParameter page) {
        PageData<AdjustFeeDtl> pageData = new PageData<AdjustFeeDtl>(null, 0);
        try {
            pageData = agentServiceFeeService.feeDefaultFeeQuery(billMonth, accId, page);
        } catch (Exception e) {
            logger.error("合作方欠费查询失败", e);
        }
        return pageData;
    }


    @RequestMapping(value = "/page/serviceFeeAppraiseAdd")
    public String serviceFeeAppraiseAddPage() {
        return "award/serviceFeeAppraiseAdd";
    }


    @RequestMapping(value = "/page/serviceFeeAppraiseManage")
    public String serviceFeeAppraiseManagePage() {
        return "award/serviceFeeAppraiseManage";
    }

    @RequestMapping(value = "/serviceFeeAppraiseAdd")
    @ResponseBody
    public Map<String, String> serviceFeeAppraiseAdd(HttpServletRequest request, HttpServletResponse response,
                                                     AgentServiceFeeAppraise agentServiceFeeAppraise) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            ;
            agentServiceFeeAppraise.setOrgId(sPrivData.getOrgId());
            agentServiceFeeAppraise.setOpId(sPrivData.getOpId());
            agentServiceFeeService.addAgentServiceFeeAppraise(agentServiceFeeAppraise);
            reMap.put("status", "0");
            reMap.put("message", "保存成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }

    /**
     * 第三方行销项目支撑费录入
     */
    @RequestMapping(value = "/page/threeSupportFeeAdd")
    public String threeSupportFeeAdd() {
        return "award/threeSupportFeeAdd";
    }

    /**
     * 第三方行销项目支撑费录入
     */
    @RequestMapping(value = "/threeSupportFeeAdd")
    @ResponseBody
    public Map<String, String> threeSupportFeeAdd(HttpServletRequest request, HttpServletResponse response,
                                                    ThreeSupportFeeAdd threeSupportFeeAdd) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if(sPrivData == null){
                reMap.put("status", "sPrivDataError");
                reMap.put("messager", "登录时间过长，请重新登录");
                return reMap;
            }
            threeSupportFeeAdd.setOrgId(sPrivData.getOrgId());
            threeSupportFeeAdd.setOpId(sPrivData.getOpId());
            threeSupportFeeAdd.setUserName(sPrivData.getUserName());

            agentServiceFeeService.addThreeSupportFee(threeSupportFeeAdd,sPrivData);

            reMap.put("status", "0");
            reMap.put("message", "保存成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }



    /**
     * 第三方行销项目支撑费管理
     */
    @RequestMapping(value = "/page/threeSupportFeeManage")
    public String threeSupportFeeManage() {
        return "award/threeSupportFeeManage";
    }
    /**
     * 第三方行销人员考核录入
     */
    @RequestMapping(value = "/page/busiThirdXXAssessAdd")
    public String busiThirdXXAssessAdd() {
        return "award/busiThirdXXAssessAdd";
    }

    /**
     * 第三方行销人员考核管理
     */
    @RequestMapping(value = "/page/busiThirdXXAssessManage")
    public String busiThirdXXAssessManage() {
        return "award/busiThirdXXAssessManage";
    }

    /**
     * 第三方直销角色调整申请
     */
    @RequestMapping(value = "/page/busiThirdSalesRoleUpdAdd")
    public String busiThirdSalesRoleUpdAdd() {
        return "award/busiThirdSalesRoleUpdAdd";
    }

    /**
     * 第三方直销角色调整查询
     */
    @RequestMapping(value = "/page/busiThirdSalesRoleUpdManage")
    public String busiThirdSalesRoleUpdManage() {
        return "award/busiThirdSalesRoleUpdManage";
    }

    /**
     * 属地第三方行销即时激励录入
     */
    @RequestMapping(value = "/page/dependencyBusiThirdXXJSJLAdd")
    public String dependencyBusiThirdXXJSJLAdd() {
        return "award/dependencyBusiThirdXXJSJLAdd";
    }

    /**
     * 属地第三方行销即时激励管理
     */
    @RequestMapping(value = "/page/dependencyBusiThirdXXJSJLManage")
    public String dependencyBusiThirdXXJSJLManage() {
        return "award/dependencyBusiThirdXXJSJLManage";
    }



    /**
     * 第三方直销反向折算机制时间设置录入
     */
    @RequestMapping(value = "/page/busiThirdSetDateAdd")
    public String busiThirdSetDateAdd() {
        return "award/busiThirdSetDateAdd";
    }

    /**
     * 第三方直销反向折算机制时间设置查询
     */
    @RequestMapping(value = "/page/busiThirdSetDateManage")
    public String busiThirdSetDateManage() {
        return "award/busiThirdSetDateManage";
    }


    /**
     * 属地第三方直销服务单价自定义系数录入
     */
    @RequestMapping(value = "/page/busiThirdPriceServiceRatioAdd")
    public String busiThirdPriceServiceRatioAdd() {
        return "award/busiThirdPriceServiceRatioAdd";
    }

    /**
     * 属地第三方直销服务单价自定义系数管理
     */
    @RequestMapping(value = "/page/busiThirdPriceServiceRatioManage")
    public String busiThirdPriceServiceRatioManage() {
        return "award/busiThirdPriceServiceRatioManage";
    }

    @RequestMapping(value = "/page/tieTongSupportFeeAdd")
    public String tieTongSupportFeeAdd() {
        return "award/tieTongSupportFeeAdd";
    }

    @RequestMapping(value = "/page/tieTongSupportFeeManage")
    public String tieTongSupportFeeManage() {
        return "award/tieTongSupportFeeManage";
    }
    @RequestMapping(value = "/page/busiThirdDependRoleManage")
    public String busiThirdDependRoleManage() {
        return "award/busiThirdDependRoleManage";
    }

    @RequestMapping(value = "/page/busiThirdSpecialOfferInfoAdd")
    public String busiThirdSpecialOfferInfoAdd() {
        return "award/busiThirdSpecialOfferInfoAdd";
    }

    @RequestMapping(value = "/page/busiThirdSpecialOfferInfoManage")
    public String busiThirdSpecialOfferInfoManage() {
        return "award/busiThirdSpecialOfferInfoManage";
    }
    
    
    /**
     *第三方行销项目支撑费管理
     */

    @RequestMapping(value = "/threeSupportFeeManage")
    @ResponseBody
    public PageData<ThreeSupportFeeAdd> threeSupportFeeManage(HttpServletRequest request, HttpServletResponse response,
                                                              String begUpLoadDate,String endUpLoadDate,
                                                                PageParameter page) {

        PageData<ThreeSupportFeeAdd> pageData = null;
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if(sPrivData == null){
                throw new Exception("登录时间过长，请重新登录");
            }
            pageData = agentServiceFeeService.ManageThreeSupportFee(begUpLoadDate,endUpLoadDate, page);
        } catch (Exception e) {
            pageData = new PageData<ThreeSupportFeeAdd>(new ArrayList<ThreeSupportFeeAdd>(), 0);
            logger.error("查询失败", e);
        }
        return pageData;
    }

    /*
     * 第三方行销项目支撑费审批 查询
     */
    @RequestMapping(value = "/threeSupportFeeInfoQuery")
    @ResponseBody
    public PageData<ThreeSupportFeeAdd> threeSupportFeeInfoQuery(HttpServletRequest request, HttpServletResponse response,
                                                                ThreeSupportFeeAdd threeSupportFeeAdd, PageParameter page) throws Exception {
        PageData<ThreeSupportFeeAdd> pageData = null;
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        if (sPrivData == null){
            logger.error("登录信息不存在！");
            throw new Exception("登录信息不存在！");
        }
        try {
            threeSupportFeeAdd.setOrgId(sPrivData.getOrgId());
            threeSupportFeeAdd.setUserName(sPrivData.getUserName());
            pageData = agentServiceFeeService.threeSupportFeeInfoQuery(threeSupportFeeAdd, page);
        } catch (Exception e) {
            pageData = new PageData<ThreeSupportFeeAdd>(new ArrayList<ThreeSupportFeeAdd>(), 0);
            logger.error("查询失败", e);
        }
        return pageData;
    }

    /**
     *物流OAO服务项目供应商服务费考核录入
     */
    @RequestMapping(value = "/page/OAOExamineAdd")
    public String OAOExamineAdd(){
        return "award/OAOExamineAdd";
    }

    /**
     * 物流OAO服务项目供应商服务费考核录入
     */
    @RequestMapping(value = "/OAOExamineAdd")
    @ResponseBody
    public Map<String, String> OAOExamineAdd(HttpServletRequest request, HttpServletResponse response,
                                             OAOExamineAdd oAOExamineAdd) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if(sPrivData == null){
                reMap.put("status", "sPrivDataError");
                reMap.put("messager", "登录时间过长，请重新登录");
                return reMap;
            }
            oAOExamineAdd.setOrgId(sPrivData.getOrgId());
            oAOExamineAdd.setOpId(sPrivData.getOpId());

            agentServiceFeeService.addOAOExamine(oAOExamineAdd);

            reMap.put("status", "0");
            reMap.put("message", "保存成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }


    /**
     * 物流OAO服务项目供应商服务费考核管理
     */
    @RequestMapping(value = "/page/OAOExamineManage")
    public String OAOExamineManage() {
        return "award/OAOExamineManage";
    }

    /**
     *物流OAO服务项目供应商服务费考核管理
     */

    @RequestMapping(value = "/OAOExamineManage")
    @ResponseBody
    public PageData<OAOExamineAdd> OAOExamineManage(HttpServletRequest request, HttpServletResponse response,
                                                    OAOExamineAdd oAOExamineAdd,
                                                              PageParameter page) {

        PageData<OAOExamineAdd> pageData = null;
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if(sPrivData == null){
                throw new Exception("登录时间过长，请重新登录");
            }
            pageData = agentServiceFeeService.ManageOAOExamine(oAOExamineAdd, page);
        } catch (Exception e) {
            pageData = new PageData<OAOExamineAdd>(new ArrayList<OAOExamineAdd>(), 0);
            logger.error("查询失败", e);
        }
        return pageData;
    }

    /**
     * 物流OAO服务项目供应商服务费考核管理
     *
     * @param request
     * @param response
     * @param oaoExamineAdd
     * @return
     */
    @RequestMapping(value = "/OAOExamineEdit")
    @ResponseBody
    public Map<String, String> OAOExamineEdit(HttpServletRequest request, HttpServletResponse response,
                                                     OAOExamineAdd oaoExamineAdd) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if(sPrivData == null){
                throw new Exception("登录时间过长，请重新登录");
            }

            oaoExamineAdd.setOrgId(sPrivData.getOrgId());
            oaoExamineAdd.setOpId(sPrivData.getOpId());
            agentServiceFeeService.EditOAOExamine(oaoExamineAdd);
            reMap.put("status", "0");
            reMap.put("message", "保存成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }

    /**
     * 加盟营业厅营销支撑费考核录入
     */
    @RequestMapping(value = "/page/assessFeeAppraiseAdd")
    public String assessFeeAppraiseAddPage() {
        return "award/assessFeeAppraiseAdd";
    }

    /**
     * 加盟营业厅营销支撑费考核录入
     *
     * @param request
     * @param response
     * @param agentAssessFeeAppraise
     * @return
     */
    @RequestMapping(value = "/assessFeeAppraiseAdd")
    @ResponseBody
    public Map<String, String> assessFeeAppraiseAdd(HttpServletRequest request, HttpServletResponse response,
                                                    AgentAssessFeeAppraise agentAssessFeeAppraise) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            agentAssessFeeAppraise.setOrgId(sPrivData.getOrgId());
            agentAssessFeeAppraise.setOpId(sPrivData.getOpId());
            agentServiceFeeService.addAgentAssessFeeAppraise(agentAssessFeeAppraise);
            reMap.put("status", "0");
            reMap.put("message", "保存成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }

    /**
     * 加盟营业厅营销支撑费考核管理
     */
    @RequestMapping(value = "/page/assessFeeAppraiseManage")
    public String assessFeeAppraiseManagePage() {
        return "award/assessFeeAppraiseManage";
    }

    /**
     * 加盟营业厅营销支撑费考核信息
     *
     * @param request
     * @param response
     * @param agentAssessFeeAppraise
     * @param page
     * @return
     */
    @RequestMapping(value = "/assessFeeAppraiseQuery")
    @ResponseBody
    public PageData<AgentAssessFeeAppraise> assessFeeAppraiseQuery(HttpServletRequest request, HttpServletResponse response,
                                                                   AgentAssessFeeAppraise agentAssessFeeAppraise, PageParameter page) {
        PageData<AgentAssessFeeAppraise> pageData = null;
        try {
            pageData = agentServiceFeeService.queryAgentAssessFeeAppraise(agentAssessFeeAppraise, page);
        } catch (Exception e) {
            pageData = new PageData<AgentAssessFeeAppraise>(new ArrayList<AgentAssessFeeAppraise>(), 0);
            logger.error("查询失败", e);
        }
        return pageData;
    }

    /**
     * 加盟营业厅营销支撑费考核管理
     *
     * @param request
     * @param response
     * @param agentAssessFeeAppraise
     * @return
     */
    @RequestMapping(value = "/assessFeeAppraiseEdit")
    @ResponseBody
    public Map<String, String> assessFeeAppraiseEdit(HttpServletRequest request, HttpServletResponse response,
                                                     AgentAssessFeeAppraise agentAssessFeeAppraise) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            ;
            agentAssessFeeAppraise.setOrgId(sPrivData.getOrgId());
            agentAssessFeeAppraise.setOpId(sPrivData.getOpId());
            agentServiceFeeService.editAgentAssessFeeAppraise(agentAssessFeeAppraise);
            reMap.put("status", "0");
            reMap.put("message", "保存成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }

    /**
     * 加盟营业厅营销支撑费考核删除
     *
     * @param request
     * @param response
     * @param agentAssessFeeAppraise
     * @return
     */
    @RequestMapping(value = "/assessFeeAppraiseDel")
    @ResponseBody
    public Map<String, String> assessFeeAppraiseDel(HttpServletRequest request, HttpServletResponse response,
                                                    AgentAssessFeeAppraise agentAssessFeeAppraise) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            ;
            agentAssessFeeAppraise.setOrgId(sPrivData.getOrgId());
            agentAssessFeeAppraise.setOpId(sPrivData.getOpId());
            agentServiceFeeService.delAgentAssessFeeAppraise(agentAssessFeeAppraise);
            reMap.put("status", "0");
            reMap.put("message", "删除成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }

    @RequestMapping(value = "/serviceFeeAppraiseEdit")
    @ResponseBody
    public Map<String, String> serviceFeeAppraiseEdit(HttpServletRequest request, HttpServletResponse response,
                                                      AgentServiceFeeAppraise agentServiceFeeAppraise) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            ;
            agentServiceFeeAppraise.setOrgId(sPrivData.getOrgId());
            agentServiceFeeAppraise.setOpId(sPrivData.getOpId());
            agentServiceFeeService.editAgentServiceFeeAppraise(agentServiceFeeAppraise);
            reMap.put("status", "0");
            reMap.put("message", "保存成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }

    @RequestMapping(value = "/serviceFeeAppraiseDel")
    @ResponseBody
    public Map<String, String> serviceFeeAppraiseDel(HttpServletRequest request, HttpServletResponse response,
                                                     AgentServiceFeeAppraise agentServiceFeeAppraise) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            ;
            agentServiceFeeAppraise.setOrgId(sPrivData.getOrgId());
            agentServiceFeeAppraise.setOpId(sPrivData.getOpId());
            agentServiceFeeService.delAgentServiceFeeAppraise(agentServiceFeeAppraise);
            reMap.put("status", "0");
            reMap.put("message", "删除成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }

    @RequestMapping(value = "/serviceFeeAppraiseQuery")
    @ResponseBody
    public PageData<AgentServiceFeeAppraise> serviceFeeAppraiseQuery(HttpServletRequest request, HttpServletResponse response,
                                                                     AgentServiceFeeAppraise agentServiceFeeAppraise, PageParameter page) {
        PageData<AgentServiceFeeAppraise> pageData = null;
        try {
            pageData = agentServiceFeeService.queryAgentServiceFeeAppraise(agentServiceFeeAppraise, page);
        } catch (Exception e) {
            pageData = new PageData<AgentServiceFeeAppraise>(new ArrayList<AgentServiceFeeAppraise>(), 0);
            logger.error("查询失败", e);
        }
        return pageData;
    }


    @RequestMapping(value = "/page/leagueNodeClassifyInfoAdd")
    public String leagueNodeClassifyInfoAddPage() {
        return "award/leagueNodeClassifyInfoAdd";
    }

    @RequestMapping(value = "/page/leagueNodeClassifyInfoManage")
    public String leagueNodeClassifyInfoManagePage() {
        return "award/leagueNodeClassifyInfoManage";
    }

    @RequestMapping(value = "/leagueNodeClassifyInfoAdd")
    @ResponseBody
    public Map<String, String> leagueNodeClassifyInfoAdd(HttpServletRequest request, HttpServletResponse response,
                                                         LeagueNodeClassifyInfo leagueNodeClassifyInfo) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            leagueNodeClassifyInfo.setOrgId(sPrivData.getOrgId());
            leagueNodeClassifyInfo.setOpId(sPrivData.getOpId());
            agentServiceFeeService.addLeagueNodeClassifyInfo(leagueNodeClassifyInfo);
            reMap.put("status", "0");
            reMap.put("message", "保存成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }

    @RequestMapping(value = "/leagueNodeClassifyInfoBatchAdd")
    @ResponseBody
    public Map<String, String> leagueNodeClassifyInfoBatchAdd(HttpServletRequest request, HttpServletResponse response,
                                                              @RequestParam(value = "fileName", required = false) String fileName,
                                                              @RequestParam MultipartFile[] files) {
        Map<String, String> resultMap = new HashMap<String, String>();
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        try {
            fileName = java.net.URLDecoder.decode(URLDecoder.decode(fileName, "UTF-8"), "UTF-8");
            if (StringUtils.isBlank(fileName)) {
                throw new Exception("请选择要上传的文件!");
            } else if (!"XLSX".equals(fileName.substring(fileName.indexOf(".") + 1).toUpperCase())) {
                throw new Exception("请选择XLSX格式文件!");
            }
            for (MultipartFile mulFile : files) {
                List<List<List<Object>>> data = ExcelUtil.readExcelWithHeader(2, mulFile.getInputStream());
                if (data.size() <= 0) {
                    throw new Exception("文件无有效记录！");
                }
                List<List<Object>> returnList = agentServiceFeeService.batchAddLeagueNodeClassifyInfo(data.get(0), sPrivData.getOpId(), sPrivData.getOrgId());
                request.getSession().removeAttribute("leagueNodeClassifyInfoBatchAdd");
                request.getSession().setAttribute("singleInactivePhoneUploadInfo", returnList);
            }
            resultMap.put("status", "0");
        } catch (Exception e) {
            resultMap.put("status", "1");
            resultMap.put("message", e.getMessage());
            logger.error("批量导入失败！", e);
        }
        return resultMap;
    }

    @RequestMapping(value = "/leagueNodeClassifyInfoBatchAddResultExport")
    public void leagueNodeClassifyInfoBatchAddResultExport(
            HttpServletRequest request, HttpServletResponse response) {
        String fileName = "加盟营业厅分类信息批量录入结果";
        try {
            fileName = URLEncoder.encode(fileName, "UTF-8") + ".xls";
        } catch (UnsupportedEncodingException e1) {
            logger.error("", e1);
        }
        response.setHeader("Cache-Control", "no-cache");
        response.setCharacterEncoding("UTF-8");
        response.addHeader("expires", "0");
        response.reset();
        response.resetBuffer();
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        String agent = request.getHeader("USER-AGENT");
        if (agent != null && agent.indexOf("Firefox") != -1) {
            response.addHeader("Content-Disposition", "attachment; filename*=\"utf8''" + fileName + "\"");
        } else {
            response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
        }
        try {
            OutputStream outputStream = response.getOutputStream();
            List<List<Object>> returnList = new ArrayList<List<Object>>();
            if (request.getSession().getAttribute("singleInactivePhoneUploadInfo") != null) {
                returnList = (List<List<Object>>) request.getSession().getAttribute("singleInactivePhoneUploadInfo");
            }
            List<String> columns = new ArrayList<String>();
            if (returnList.size() > 0) {
                for (Object obj : returnList.get(0)) {
                    columns.add(obj == null ? "" : obj.toString());
                }
            } else {
                columns.add("当前无可导出数据！");
            }
            if (returnList.size() > 1) {
                returnList.remove(0);
            }
            ExcelUtil.exportExcel("2", "明细", columns, returnList, outputStream);

            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            logger.error("", e);
        }
    }


    @RequestMapping(value = "/leagueNodeClassifyInfoEdit")
    @ResponseBody
    public Map<String, String> leagueNodeClassifyInfoEdit(HttpServletRequest request, HttpServletResponse response,
                                                          LeagueNodeClassifyInfo leagueNodeClassifyInfo) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            ;
            leagueNodeClassifyInfo.setOrgId(sPrivData.getOrgId());
            leagueNodeClassifyInfo.setOpId(sPrivData.getOpId());
            agentServiceFeeService.editLeagueNodeClassifyInfo(leagueNodeClassifyInfo);
            reMap.put("status", "0");
            reMap.put("message", "保存成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }

    @RequestMapping(value = "/leagueNodeClassifyInfoDel")
    @ResponseBody
    public Map<String, String> leagueNodeClassifyInfoDel(HttpServletRequest request, HttpServletResponse response,
                                                         LeagueNodeClassifyInfo leagueNodeClassifyInfo) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            ;
            leagueNodeClassifyInfo.setOrgId(sPrivData.getOrgId());
            leagueNodeClassifyInfo.setOpId(sPrivData.getOpId());
            agentServiceFeeService.delLeagueNodeClassifyInfo(leagueNodeClassifyInfo);
            reMap.put("status", "0");
            reMap.put("message", "删除成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }

    @RequestMapping(value = "/leagueNodeClassifyInfoQuery")
    @ResponseBody
    public PageData<LeagueNodeClassifyInfo> leagueNodeClassifyInfoQuery(HttpServletRequest request, HttpServletResponse response,
                                                                        LeagueNodeClassifyInfo leagueNodeClassifyInfo, PageParameter page) {
        PageData<LeagueNodeClassifyInfo> pageData = null;
        try {
            pageData = agentServiceFeeService.queryLeagueNodeClassifyInfo(leagueNodeClassifyInfo, page);
        } catch (Exception e) {
            pageData = new PageData<LeagueNodeClassifyInfo>(new ArrayList<LeagueNodeClassifyInfo>(), 0);
            logger.error("查询失败", e);
        }
        return pageData;
    }

    /**
     * 扣罚系数录入
     *
     * @param request
     * @param response
     * @param channelEntityName
     * @param billMonth
     * @param penaltyCoefficients
     * @return
     */
    @RequestMapping("/penaltyCoefficientsAdd")
    @ResponseBody
    public Map<String, String> penaltyCoefficientsAdd(HttpServletRequest request, HttpServletResponse response,
                                                      @RequestParam(value = "channelEntityName", required = true) String channelEntityName,
                                                      @RequestParam(value = "billMonth", required = true) String billMonth,
                                                      @RequestParam(value = "penaltyCoefficients", required = false) Double penaltyCoefficients) {
        Map<String, String> map = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            channelEntityName = java.net.URLDecoder.decode(URLDecoder.decode(channelEntityName, "UTF-8"), "UTF-8");
            map = penaltyCoefficientsService.penaltyCoefficientsSave(channelEntityName, billMonth, penaltyCoefficients, sPrivData);
        } catch (Exception e) {
            logger.error("扣罚系数录入失败", e);
            map.put("status", "1");
            map.put("msg", "扣罚系数录入失败");
        }
        return map;
    }

    @RequestMapping("/penaltyCoefficientsQuery")
    @ResponseBody
    public Map<String, String> penaltyCoefficientsAdd(HttpServletRequest request, HttpServletResponse response,
                                                      @RequestParam(value = "channelEntityName", required = true) String channelEntityName,
                                                      @RequestParam(value = "billMonth", required = true) String billMonth) {
        Map<String, String> map = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            channelEntityName = java.net.URLDecoder.decode(URLDecoder.decode(channelEntityName, "UTF-8"), "UTF-8");
            PenaltyCoefficients penaltyCoefficients = penaltyCoefficientsService.penaltyCoefficientsQuery(channelEntityName, billMonth);
            Double penalty = penaltyCoefficients.getPenaltyCoefficient();
            map.put("penaltyCoefficient", String.valueOf(penalty));
        } catch (Exception e) {
            logger.error("扣罚系数查询失败", e);
            map.put("status", "3");
            map.put("msg", "扣罚系数查询失败");
        }
        return map;
    }

    /**
     * @return
     * <AUTHOR>
     * 铁通看管项目考核打分录入界面上传附件
     */
    @RequestMapping("/tietongFilesUpload")
    @ResponseBody
    public Map<String, Object> tietongFilesUpload(HttpServletRequest request, HttpServletResponse response,
                                                  @RequestParam(value = "billMonth", required = true) String billMonth,
                                                  @RequestParam(value = "fileName", required = true) String fileName,
                                                  @RequestParam MultipartFile[] files) {

        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            for (MultipartFile multipartFile : files) {
                if (!multipartFile.isEmpty()) {
                    //服务器IP
                    List<ChannelSysBaseType> channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 0, null, null);
                    String ip = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器用户名
                    channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 1, null, null);
                    String username = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器密码
                    channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 2, null, null);
                    String password = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器文件路径
                    channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 126, null, null);
                    String remotePath = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器文件
                    String remoteFileName = multipartFile.getOriginalFilename();
                    String remoteFile = remotePath + billMonth + "_" + UUID.randomUUID().toString();
                    SecureFileTransferProtocol.upload(ip, username, password, multipartFile.getInputStream(), remoteFile);
                    resultMap.put("remoteFileName", remoteFileName);
                    resultMap.put("remotePath", remoteFile);
                }
            }
            resultMap.put("status", "1");
            resultMap.put("msg", "文件上传成功！");
        } catch (Exception e) {
            resultMap.put("status", "0");
            resultMap.put("msg", "文件上传失败！");
            logger.error("文件上传失败！", e);
        }
        return resultMap;
    }

    /**
     * @Description 校验当月铁通看管项目考核打分是否已经录入
     * -----取消限制了，现在这个方法没用，正常情况都是返回的status都是0
     * @Params [request, response, channelEntityName, billMonth]
     * @Return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @CreateDate 2020/10/12
     */
    @RequestMapping("/checkTietongScoreIsExist")
    @ResponseBody
    public Map<String, Object> checkTietongScoreIsExist(HttpServletRequest request, HttpServletResponse response,
                                                        @RequestParam(value = "channelEntityName", required = true) String channelEntityName,
                                                        @RequestParam(value = "billMonth", required = true) String billMonth) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            channelEntityName = URLDecoder.decode(URLDecoder.decode(channelEntityName, "UTF-8"), "UTF-8");
            TietongItemScore tietongItemScore = new TietongItemScore();
            tietongItemScore.setBillMonth(billMonth);
            Boolean flag = agentServiceFeeService.checkTietongScoreIsExist(channelEntityName, tietongItemScore);
            // 已经存在对应数据，现在开始将ftp上的对应文件删除
            if (flag == true) {
                //服务器IP
                List<ChannelSysBaseType> channelSysBaseTypeList =
                        ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 0, null, null);
                String ip = channelSysBaseTypeList.get(0).getCodeName();
                //服务器用户名
                channelSysBaseTypeList =
                        ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 1, null, null);
                String username = channelSysBaseTypeList.get(0).getCodeName();
                //服务器密码
                channelSysBaseTypeList =
                        ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 2, null, null);
                String password = channelSysBaseTypeList.get(0).getCodeName();
                String filePath=agentServiceFeeService.getRemoteFilePath2(billMonth);
//                删除对应文件，这个文件上传之后就通过UUID.randomUUID()乱码处理了，选中这个路径就可以删除了
                    SecureFileTransferProtocol.deleteFile(ip, username, password, filePath);
//                将数据库中对应的数据的rec_status置为0
                agentServiceFeeService.resetRecStatus2(billMonth);
                resultMap.put("status", "0");
            } else {
                // 录入的数据在库中不存在，直接进行下一步操作
                resultMap.put("status", "0");
            }
        } catch (Exception e) {
            resultMap.put("status", "1");
            resultMap.put("msg", "代理商铁通的相关信息不存在！");
            logger.error("代理商铁通的相关信息不存在！！" + e);
        }
        return resultMap;
    }

    /**
     * @Description 铁通看管项目考核打分录入--保存
     * @Params [request, response, channelEntityName, billMonth, runScore, supportPeoples, floatCoefficient, fileName, filePath]
     * @Return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @CreateDate 2020/10/12
     */
    @RequestMapping("/TietongItemScoreSave")
    @ResponseBody
    public Map<String, Object> TietongItemScoreSave(HttpServletRequest request, HttpServletResponse response,
                                                    @RequestParam(value = "channelEntityName", required = true) String channelEntityName,
                                                    @RequestParam(value = "billMonth", required = true) String billMonth,
                                                    @RequestParam(value = "runScore", required = true) Integer runScore,
                                                    @RequestParam(value = "supportPeoples", required = true) Integer supportPeoples,
                                                    @RequestParam(value = "floatCoefficient", required = true) Float floatCoefficient,
                                                    @RequestParam(value = "remoteFileName", required = true) String fileName,
                                                    @RequestParam(value = "remotePath", required = true) String filePath) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if (sPrivData == null) {
                resultMap.put("status", "0");
                resultMap.put("msg", "登陆超时，请重新登陆！");
                return resultMap;
            }
            channelEntityName = URLDecoder.decode(URLDecoder.decode(channelEntityName, "UTF-8"), "UTF-8");
            TietongItemScore tietongItemScore = new TietongItemScore();
            tietongItemScore.setRunScore(runScore);
            tietongItemScore.setBillMonth(billMonth);
            tietongItemScore.setSupportPeoples(supportPeoples);
            tietongItemScore.setFloatCoefficient(floatCoefficient);
            tietongItemScore.setFileName(fileName);
            tietongItemScore.setFilePath(filePath);
            tietongItemScore.setOpId(sPrivData.getOpId());
            tietongItemScore.setOrgId(sPrivData.getOrgId());
            tietongItemScore.setCreateDate(DateUtil.getCurrDate());
            tietongItemScore.setDoneDate(DateUtil.getCurrDate());
            tietongItemScore.setRecStatus(1);
            agentServiceFeeService.tietongScoresSave(channelEntityName, tietongItemScore);
            resultMap.put("status", "1");
            resultMap.put("msg", "铁通看管项目考核打分录入成功！");
        } catch (Exception e) {
            resultMap.put("status", "0");
            resultMap.put("msg", "铁通看管项目考核打分录入失败！");
            logger.error("铁通看管项目考核打分录入保存失败！！" + e);
        }
        return resultMap;
    }

    /**
     * @Description 查询铁通看管项目考核打分信息
     * @Params [request, response, channelEntityName, billMonth, page]
     * @Return com.ailk.newchnl.mybatis.pagination.PageData<com.ailk.newchnl.entity.TietongItemScore>
     * <AUTHOR>
     * @CreateDate 2020/10/12
     */
    @RequestMapping("/getTietongScoreInfo")
    @ResponseBody
    public PageData<TietongItemScore> getTietongScoreInfo(HttpServletRequest request, HttpServletResponse response,
                                                          @RequestParam(value = "channelEntityName", required = true) String channelEntityName,
                                                          @RequestParam(value = "billMonth", required = true) String billMonth,
                                                          PageParameter page) {
        PageData pageData = new PageData(null, 0);
        try {
            channelEntityName = URLDecoder.decode(URLDecoder.decode(channelEntityName, "UTF-8"), "UTF-8");
            TietongItemScore tietongItemScore = new TietongItemScore();
            tietongItemScore.setBillMonth(billMonth);
            pageData = agentServiceFeeService.queryTietongScore(tietongItemScore, page);
        } catch (Exception e) {
            logger.error("查询铁通看管项目考核打分记录失败！", e);
        }
        return pageData;
    }

    @RequestMapping("/getTietongScoreDetailInfo")
    @ResponseBody
    public List<TietongItemScore> getTietongScoreDetailInfo(HttpServletRequest request, HttpServletResponse response,
                                                            @RequestParam(value = "channelEntityName", required = true) String channelEntityName,
                                                            @RequestParam(value = "billMonth", required = true) String billMonth) {
        List<TietongItemScore> tietongItemScoreList = null;
        try {
            channelEntityName = URLDecoder.decode(URLDecoder.decode(channelEntityName, "UTF-8"), "UTF-8");
            TietongItemScore tietongItemScore = new TietongItemScore();
            tietongItemScore.setBillMonth(billMonth);
            tietongItemScoreList = agentServiceFeeService.queryTietongDetailScoreInfo(tietongItemScore);
        } catch (Exception e) {
            logger.error("查询在线公司考核得分失败！", e);
        }
        return tietongItemScoreList;

    }

    /**
     * @Description 铁通看管项目考核打分录入--修改
     * @Params [request, response, channelEntityName, billMonth, runScore, supportPeoples, floatCoefficient, fileName, filePath]
     * @Return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @CreateDate 2020/10/12
     */
    @RequestMapping("/TietongItemScoreModify")
    @ResponseBody
    public Map<String, Object> TietongItemScoreModify(HttpServletRequest request, HttpServletResponse response,
                                                      @RequestParam(value = "channelEntityName", required = true) String channelEntityName,
                                                      @RequestParam(value = "billMonth", required = true) String billMonth,
                                                      @RequestParam(value = "runScore", required = true) Integer runScore,
                                                      @RequestParam(value = "supportPeoples", required = true) Integer supportPeoples,
                                                      @RequestParam(value = "floatCoefficient", required = true) Float floatCoefficient,
                                                      @RequestParam(value = "remoteFileName", required = true) String fileName,
                                                      @RequestParam(value = "remotePath", required = true) String filePath) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if (sPrivData == null) {
                resultMap.put("status", "0");
                resultMap.put("msg", "登陆超时，请重新登陆！");
                return resultMap;
            }
            channelEntityName = URLDecoder.decode(URLDecoder.decode(channelEntityName, "UTF-8"), "UTF-8");
            TietongItemScore tietongItemScore = new TietongItemScore();
            tietongItemScore.setRunScore(runScore);
            tietongItemScore.setBillMonth(billMonth);
            tietongItemScore.setSupportPeoples(supportPeoples);
            tietongItemScore.setFloatCoefficient(floatCoefficient);
            tietongItemScore.setFileName(fileName);
            tietongItemScore.setFilePath(filePath);
            tietongItemScore.setOpId(sPrivData.getOpId());
            tietongItemScore.setOrgId(sPrivData.getOrgId());
            tietongItemScore.setCreateDate(DateUtil.getCurrDate());
            tietongItemScore.setDoneDate(DateUtil.getCurrDate());
            tietongItemScore.setRecStatus(1);
            agentServiceFeeService.tietongScoresSave(channelEntityName, tietongItemScore);
            resultMap.put("status", "1");
            resultMap.put("msg", "铁通看管项目考核打分修改成功！");
        } catch (Exception e) {
            resultMap.put("status", "0");
            resultMap.put("msg", "铁通看管项目考核打分修改失败！");
            logger.error("铁通看管项目考核打分修改失败！！" + e);
        }
        return resultMap;
    }

    /**
     * @Description 校验当月业务集中稽核考核录入是否已经录入
     * @Params [request, response, channelEntityName, billMonth]
     * @Return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @CreateDate 2020/11/17
     */
    @RequestMapping("/checkContentIsExist")
    @ResponseBody
    public Map<String, Object> checkContentIsExist(HttpServletRequest request, HttpServletResponse response,
                                                   @RequestParam(value = "channelEntityName", required = true) String channelEntityName,
                                                   @RequestParam(value = "billMonth", required = true) String billMonth) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            channelEntityName = URLDecoder.decode(URLDecoder.decode(channelEntityName, "UTF-8"), "UTF-8");
            CheckContentScore checkContentScore = new CheckContentScore();
            checkContentScore.setBillMonth(billMonth);
            Boolean flag = agentServiceFeeService.checkContentIsExist(channelEntityName, checkContentScore);
            if (flag == true) {
                resultMap.put("status", "1");
                resultMap.put("msg", "本月的业务集中稽核考核打分已经存在，不可重复录入！");
            } else {
                resultMap.put("status", "0");
            }
        } catch (Exception e) {
            resultMap.put("status", "2");
            resultMap.put("msg", "校验业务集中稽核考核打分是否已经录入失败！");
            logger.error("校验当月的业务集中稽核考核打分是否已经录入失败！！" + e);
        }
        return resultMap;
    }

    /**
     * @return
     * <AUTHOR>
     * 业务集中稽核考核得分录入界面上传附件
     */
    @RequestMapping("/checkContentFilesUpload")
    @ResponseBody
    public Map<String, Object> checkContentFilesUpload(HttpServletRequest request, HttpServletResponse response,
                                                       @RequestParam(value = "billMonth", required = true) String billMonth,
                                                       @RequestParam(value = "fileName", required = true) String fileName,
                                                       @RequestParam MultipartFile[] files) {

        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            for (MultipartFile multipartFile : files) {
                if (!multipartFile.isEmpty()) {
                    //服务器IP
                    List<ChannelSysBaseType> channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 0, null, null);
                    String ip = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器用户名
                    channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 1, null, null);
                    String username = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器密码
                    channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 2, null, null);
                    String password = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器文件路径
                    channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 127, null, null);
                    String remotePath = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器文件
                    String remoteFileName = multipartFile.getOriginalFilename();
                    String remoteFile = remotePath + billMonth + "_" + UUID.randomUUID().toString();
                    SecureFileTransferProtocol.upload(ip, username, password, multipartFile.getInputStream(), remoteFile);
                    resultMap.put("remoteFileName", remoteFileName);
                    resultMap.put("remotePath", remoteFile);
                }
            }
            resultMap.put("status", "1");
            resultMap.put("msg", "文件上传成功！");
        } catch (Exception e) {
            resultMap.put("status", "0");
            resultMap.put("msg", "文件上传失败！");
            logger.error("文件上传失败！", e);
        }
        return resultMap;
    }

    /**
     * @Description 业务集中稽核考核打分录入--保存
     * @Params [request, response, channelEntityName, billMonth, score1, score2, score3, score4, score5, allScore, fileName, filePath]
     * @Return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @CreateDate 2020/11/17
     */
    @RequestMapping("/checkContentScoreSave")
    @ResponseBody
    public Map<String, Object> checkContentScoreSave(HttpServletRequest request, HttpServletResponse response,
                                                     @RequestParam(value = "channelEntityName", required = true) String channelEntityName,
                                                     @RequestParam(value = "billMonth", required = true) String billMonth,
                                                     @RequestParam(value = "score1", required = true) Float score1,
                                                     @RequestParam(value = "score2", required = true) Float score2,
                                                     @RequestParam(value = "score3", required = true) Float score3,
                                                     @RequestParam(value = "score4", required = true) Float score4,
                                                     @RequestParam(value = "score5", required = true) String score5,
                                                     @RequestParam(value = "allScore", required = true) Float allScore,
                                                     @RequestParam(value = "remoteFileName", required = true) String fileName,
                                                     @RequestParam(value = "remotePath", required = true) String filePath) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if (sPrivData == null) {
                resultMap.put("status", "0");
                resultMap.put("msg", "登陆超时，请重新登陆！");
                return resultMap;
            }
            channelEntityName = URLDecoder.decode(URLDecoder.decode(channelEntityName, "UTF-8"), "UTF-8");
            List<ChannelSysBaseType> channelSysBaseTypeList = agentServiceFeeService.queryCodeName(channelEntityName);
            String CodeName = channelSysBaseTypeList.get(0).getCodeName();
            CheckContentScore checkContentScore = new CheckContentScore();
            checkContentScore.setScore1(score1);
            checkContentScore.setScore2(score2);
            checkContentScore.setScore3(score3);
            checkContentScore.setScore4(score4);
            checkContentScore.setScore5(score5);
            checkContentScore.setBillMonth(billMonth);
            checkContentScore.setAllScore(allScore);
            checkContentScore.setFileName(fileName);
            checkContentScore.setFilePath(filePath);
            checkContentScore.setOpId(sPrivData.getOpId());
            checkContentScore.setOrgId(sPrivData.getOrgId());
            checkContentScore.setCreateDate(DateUtil.getCurrDate());
            checkContentScore.setDoneDate(DateUtil.getCurrDate());
            checkContentScore.setRecStatus(1);
            checkContentScore.setCodeName(CodeName);
            agentServiceFeeService.checkContentScoreSave(channelEntityName, checkContentScore);
            resultMap.put("status", "1");
            resultMap.put("msg", "业务集中稽核考核打分录入成功！");
        } catch (Exception e) {
            resultMap.put("status", "0");
            resultMap.put("msg", "业务集中稽核考核打分录入失败！");
            logger.error("业务集中稽核考核打分录入保存失败！！" + e);
        }
        return resultMap;
    }

    /**
     * @Description 查询业务集中稽核考核打分信息
     * @Params [request, response, channelEntityName, billMonth, page]
     * @Return com.ailk.newchnl.mybatis.pagination.PageData<com.ailk.newchnl.entity.CheckContentScore>
     * <AUTHOR>
     * @CreateDate 2020/11/17
     */
    @RequestMapping("/getCheckContentScoreInfo")
    @ResponseBody
    public PageData<CheckContentScore> getCheckContentScoreInfo(HttpServletRequest request, HttpServletResponse response,
                                                                @RequestParam(value = "channelEntityName", required = true) String channelEntityName,
                                                                @RequestParam(value = "billMonth", required = true) String billMonth,
                                                                PageParameter page) {
        PageData pageData = new PageData(null, 0);
        try {
            channelEntityName = URLDecoder.decode(URLDecoder.decode(channelEntityName, "UTF-8"), "UTF-8");
            CheckContentScore checkContentScore = new CheckContentScore();
            checkContentScore.setBillMonth(billMonth);
            pageData = agentServiceFeeService.queryCheckContentScore(checkContentScore, page);
        } catch (Exception e) {
            logger.error("查询业务集中稽核考核打分记录失败！", e);
        }
        return pageData;
    }

    @RequestMapping("/getCheckContentScoreDetailInfo")
    @ResponseBody
    public List<CheckContentScore> getCheckContentScoreDetailInfo(HttpServletRequest request, HttpServletResponse response,
                                                                  @RequestParam(value = "channelEntityName", required = true) String channelEntityName,
                                                                  @RequestParam(value = "billMonth", required = true) String billMonth) {
        List<CheckContentScore> checkContentScoreList = null;
        try {
            channelEntityName = URLDecoder.decode(URLDecoder.decode(channelEntityName, "UTF-8"), "UTF-8");
            CheckContentScore checkContentScore = new CheckContentScore();
            checkContentScore.setBillMonth(billMonth);
            checkContentScoreList = agentServiceFeeService.queryCheckContentDetailScoreInfo(checkContentScore);
        } catch (Exception e) {
            logger.error("查询业务集中稽核考核得分失败！", e);
        }
        return checkContentScoreList;

    }

    /**
     * @Description 业务集中稽核考核打分录入--修改
     * @Params [request, response, channelEntityName, billMonth, score1, score2, score3, score4, score5, allScore, fileName, filePath]
     * @Return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @CreateDate 2020/11/18
     */
    @RequestMapping("/checkContentScoreModify")
    @ResponseBody
    public Map<String, Object> checkContentScoreModify(HttpServletRequest request, HttpServletResponse response,
                                                       @RequestParam(value = "channelEntityName", required = true) String channelEntityName,
                                                       @RequestParam(value = "billMonth", required = true) String billMonth,
                                                       @RequestParam(value = "score1", required = true) Float score1,
                                                       @RequestParam(value = "score2", required = true) Float score2,
                                                       @RequestParam(value = "score3", required = true) Float score3,
                                                       @RequestParam(value = "score4", required = true) Float score4,
                                                       @RequestParam(value = "score5", required = true) String score5,
                                                       @RequestParam(value = "allScore", required = true) Float allScore,
                                                       @RequestParam(value = "remoteFileName", required = true) String fileName,
                                                       @RequestParam(value = "remotePath", required = true) String filePath) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if (sPrivData == null) {
                resultMap.put("status", "0");
                resultMap.put("msg", "登陆超时，请重新登陆！");
                return resultMap;
            }
            channelEntityName = URLDecoder.decode(URLDecoder.decode(channelEntityName, "UTF-8"), "UTF-8");
            List<ChannelSysBaseType> channelSysBaseTypeList = agentServiceFeeService.queryCodeName(channelEntityName);
            String CodeName = channelSysBaseTypeList.get(0).getCodeName();
            CheckContentScore checkContentScore = new CheckContentScore();
            checkContentScore.setScore1(score1);
            checkContentScore.setScore2(score2);
            checkContentScore.setScore3(score3);
            checkContentScore.setScore4(score4);
            checkContentScore.setScore5(score5);
            checkContentScore.setBillMonth(billMonth);
            checkContentScore.setAllScore(allScore);
            checkContentScore.setFileName(fileName);
            checkContentScore.setFilePath(filePath);
            checkContentScore.setOpId(sPrivData.getOpId());
            checkContentScore.setOrgId(sPrivData.getOrgId());
            checkContentScore.setCreateDate(DateUtil.getCurrDate());
            checkContentScore.setDoneDate(DateUtil.getCurrDate());
            checkContentScore.setRecStatus(1);
            checkContentScore.setCodeName(CodeName);
            agentServiceFeeService.checkContentModify(channelEntityName, checkContentScore);
            resultMap.put("status", "1");
            resultMap.put("msg", "业务集中稽核考核打分修改成功！");
        } catch (Exception e) {
            resultMap.put("status", "0");
            resultMap.put("msg", "业务集中稽核考核打分修改失败！");
            logger.error("业务集中稽核考核打分修改失败！！" + e);
        }
        return resultMap;
    }

    @RequestMapping("/export")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response, @RequestParam(value = "channelEntityName") String channelEntityName,
                            @RequestParam(value = "billMonth", required = true) String billMonth,
                            @RequestParam(value = "excelName") String excelName, PageParameter page) throws Exception {

        try {
            channelEntityName = URLDecoder.decode(URLDecoder.decode(channelEntityName, "UTF-8"), "UTF-8");
        } catch (UnsupportedEncodingException e1) {
            logger.warn("参数解码失败", e1);
        }

        // 构建CodeType为10033(渠道类型)对用的CodeId<--->CodeName Map
        List<ChannelSysBaseType> sysTypes = ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(10033, null, null, null);
        Map<Integer, String> sysTypeMap = new HashMap<Integer, String>();
        for (ChannelSysBaseType bType : sysTypes) {
            sysTypeMap.put(bType.getCodeId(), bType.getCodeName());
        }

        response.setHeader("Cache-Control", "no-cache");
        response.setCharacterEncoding("UTF-8");
        response.addHeader("expires", "0");
        response.reset();
        response.resetBuffer();
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        String agent = request.getHeader("USER-AGENT");
        if (agent != null && agent.indexOf("Firefox") != -1) {
            response.addHeader("Content-Disposition", "attachment; filename*=\"utf8''" + excelName + ".xls" + "\"");
        } else {
            response.addHeader("Content-Disposition", "attachment; filename=\"" + excelName + ".xls" + "\"");
        }
        try {
            OutputStream outputStream = response.getOutputStream();
            PageData<CheckContentScore> pageDate = null;
            Map<String, String> titleField = getTitleField();
            page.setPageUsed(0);
            CheckContentScore checkContentScore = new CheckContentScore();
            checkContentScore.setBillMonth(billMonth);
            pageDate = agentServiceFeeService.queryCheckContentScore(checkContentScore, page);

            List<Map<String, Object>> rows = new LinkedList<Map<String, Object>>();
            for (CheckContentScore nl : pageDate.getData()) {
                String districtName = nl.getCodeName();
                String agentName = nl.getChannelEntityName();
                String billMonths = nl.getBillMonth();
                Float score1 = nl.getScore1();
                Float score2 = nl.getScore2();
                Float score3 = nl.getScore3();
                Float score4 = nl.getScore4();
                String score5 = nl.getScore5();
                Float allScore = nl.getAllScore();
                rows.add(objToMap(nl));
            }
            List<List<Map<String, Object>>> header = new ArrayList<List<Map<String, Object>>>();
            List<Map<String, Object>> header_ = new ArrayList<Map<String, Object>>();
            String str[][] = {
                    {"", "3"},
                    {"稽核质量", "2"},
                    {"管理质量", "2"},
                    {"否决项", "1"},
                    {"", "1"}};
            for (String str_[] : str) {
                Map<String, Object> map_ = new HashMap<String, Object>();
                map_.put("title", str_[0]);
                map_.put("count", str_[1]);
                header_.add(map_);
            }
            header.add(header_);
            Integer nodeKind = 3;
            ExcelUtil.exportExcelForDataGridHasMoreHeader("1", "业务集中稽核考核评分表", formatDataMap(nodeKind), header, columns(titleField), rows, outputStream);
        } catch (Exception e) {
            logger.error("错误信息：", e);
        }
    }

    private static Map<String, String> getTitleField() {
        Map<String, String> map = new LinkedHashMap<String, String>();
        List<String> fields = new ArrayList<String>();
        List<String> titles = new ArrayList<String>();
        String[][] column = {{"", "属地", "codeName"},
                {"", "代理商", "channelEntityName"},
                {"", "考核月", "billMonth"},
                {"稽核质量", "业务稽核及时性", "score1"},
                {"稽核质量", "业务稽核准确性", "score2"},
                {"管理质量", "基础管理", "score3"},
                {"管理质量", "工作配合", "score4"},
                {"否决项", "是否重大违规", "score5"},
                {"", "总分", "allScore"}};
        for (String[] temp : column) {
            titles.add(temp[1]);
            fields.add(temp[2]);
        }
        for (int i = 0; i < fields.size(); i++) {
            map.put(fields.get(i), titles.get(i));
        }
        return map;
    }

    private static List<Map<String, String>> columns(Map<String, String> titleField) {
        List<Map<String, String>> columns = new LinkedList<Map<String, String>>();
        for (Map.Entry<String, String> entry : titleField.entrySet()) {
            Map<String, String> map = new HashMap<String, String>();
            map.put("field", entry.getKey());
            map.put("title", entry.getValue());
            columns.add(map);
        }
        return columns;
    }

    private static Map<String, Map<String, String>> formatDataMap(Integer iNodeKind) {
        Map<String, Map<String, String>> map = new HashMap<String, Map<String, String>>();
        Map<String, Integer> fieldValues = new HashMap<String, Integer>();

        fieldValues.put("nodeKind", 10033);
        fieldValues.put("districtId", 10002);
        for (Map.Entry<String, Integer> fieldValue : fieldValues.entrySet()) {
            List<ChannelSysBaseType> types = ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(fieldValue.getValue(), null, null, null);
            Map<String, String> typeMap = new HashMap<String, String>();
            for (ChannelSysBaseType type : types) {
                typeMap.put(type.getCodeId().toString(), type.getCodeName());
            }
            map.put(fieldValue.getKey(), typeMap);
        }
        return map;
    }

    private static <T> Map<String, Object> objToMap(T t) {
        Map<String, Object> map = new HashMap<String, Object>();
        Field[] fields = t.getClass().getDeclaredFields();
        for (Field field : fields) {
            try {
                String key = field.getName();
                field.setAccessible(true);
                Object value = field.get(t);
                map.put(key, value);
            } catch (Exception e) {

            }
        }
        return map;
    }
    /**
     * 加盟营业厅营销支撑费考核录入
     */
    @RequestMapping(value = "/page/assessFeeJobContractAdd")
    public String assessFeeJobContractAddPage() {
        return "award/assessFeeJobContractAdd";
    }

    /**
     * 加盟营业厅营销支撑费考核录入
     *
     * @param request
     * @param response
     * @param agentAssessFeeJobContract
     * @return
     */
    @RequestMapping(value = "/addAssessFeeJobContract")
    @ResponseBody
    public Map<String, String> assessFeeJobContractAdd(HttpServletRequest request, HttpServletResponse response,
                                                    AgentAssessFeeJobContract agentAssessFeeJobContract) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            agentAssessFeeJobContract.setOrgId(sPrivData.getOrgId());
            agentAssessFeeJobContract.setOpId(sPrivData.getOpId());
            agentServiceFeeService.addAgentAssessFeeJobContract(agentAssessFeeJobContract);
            reMap.put("status", "0");
            reMap.put("message", "保存成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }

    /**
     * 营业厅在职人员承包项目营销支撑费考核管理
     */
    @RequestMapping(value = "/page/assessFeeJobContractManage")
    public String assessFeeJobContractManagePage() {
        return "award/assessFeeJobContractManage";
    }

    /**
     * 营业厅在职人员承包项目营销支撑费考核管理考核信息
     *
     * @param request
     * @param response
     * @param agentAssessFeeJobContract
     * @param page
     * @return
     */
    @RequestMapping(value = "/assessFeeJobContractQuery")
    @ResponseBody
    public PageData<AgentAssessFeeJobContract> assessFeeJobContractQuery(HttpServletRequest request, HttpServletResponse response,
                                                                   AgentAssessFeeJobContract agentAssessFeeJobContract, PageParameter page) {
        PageData<AgentAssessFeeJobContract> pageData = null;
        try {
            pageData = agentServiceFeeService.queryAgentAssessFeeJobContract(agentAssessFeeJobContract, page);
        } catch (Exception e) {
            pageData = new PageData<AgentAssessFeeJobContract>(new ArrayList<AgentAssessFeeJobContract>(), 0);
            logger.error("查询失败", e);
        }
        return pageData;
    }

    /**
     * 营业厅在职人员承包项目营销支撑费考核管理编辑
     *
     * @param request
     * @param response
     * @param agentAssessFeeJobContract
     * @return
     */
    @RequestMapping(value = "/assessFeeJobContractEdit")
    @ResponseBody
    public Map<String, String> assessFeeJobContractEdit(HttpServletRequest request, HttpServletResponse response,
                                                     AgentAssessFeeJobContract agentAssessFeeJobContract) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            ;
            agentAssessFeeJobContract.setOrgId(sPrivData.getOrgId());
            agentAssessFeeJobContract.setOpId(sPrivData.getOpId());
            agentServiceFeeService.editAgentAssessFeeJobContract(agentAssessFeeJobContract);
            reMap.put("status", "0");
            reMap.put("message", "保存成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }

    /**
     * 营业厅在职人员承包项目营销支撑费考核管理删除
     *
     * @param request
     * @param response
     * @param agentAssessFeeJobContract
     * @return
     */
    @RequestMapping(value = "/assessFeeJobContractDel")
    @ResponseBody
    public Map<String, String> assessFeeJobContractDel(HttpServletRequest request, HttpServletResponse response,
                                                    AgentAssessFeeJobContract agentAssessFeeJobContract) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            ;
            agentAssessFeeJobContract.setOrgId(sPrivData.getOrgId());
            agentAssessFeeJobContract.setOpId(sPrivData.getOpId());
            agentServiceFeeService.delAgentAssessFeeJobContract(agentAssessFeeJobContract);
            reMap.put("status", "0");
            reMap.put("message", "删除成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }

    @RequestMapping(value = "/page/marketingSupportFeeAdd")
    public String marketingSupportFeeAddPag() {
        return "award/marketingSupportFeeAdd";
    }

    @RequestMapping(value = "/page/marketingSupportFeeManage")
    public String marketingSupportFeeManagePag() {
        return "award/marketingSupportFeeManage";
    }
    /**
     * 第三方行销项目营销支撑费考核录入
     * @param request
     * @param response
     * @param agentMarketingSupportFee
     * @return
     */
    @RequestMapping(value = "/marketingSupportFeeAdd")
    @ResponseBody
    public Map<String, String> marketingSupportFeeAdd(HttpServletRequest request, HttpServletResponse response,
                                                      AgentMarketingSupportFee agentMarketingSupportFee) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            agentMarketingSupportFee.setOrgId(sPrivData.getOrgId());
            agentMarketingSupportFee.setOpId(sPrivData.getOpId());
            agentServiceFeeService.addAgentMarketingSupportFee(agentMarketingSupportFee);
            reMap.put("status", "0");
            reMap.put("message", "保存成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }

    /**
     * 第三方行销项目营销支撑费考核信息
     * @param request
     * @param response
     * @param agentMarketingSupportFee
     * @param page
     * @return
     */
    @RequestMapping(value = "/marketingSupportFeeQuery")
    @ResponseBody
    public PageData<AgentMarketingSupportFee> marketingSupportFeeQuery(HttpServletRequest request, HttpServletResponse response,
                                                                       AgentMarketingSupportFee agentMarketingSupportFee, PageParameter page) {
        PageData<AgentMarketingSupportFee> pageData = null;
        try {
            pageData = agentServiceFeeService.queryMarketingSupportFee(agentMarketingSupportFee, page);
        } catch (Exception e) {
            pageData = new PageData<AgentMarketingSupportFee>(new ArrayList<AgentMarketingSupportFee>(), 0);
            logger.error("查询失败", e);
        }
        return pageData;
    }

    /**
     * 第三方行销项目营销支撑费考核删除
     * @param request
     * @param response
     * @param agentMarketingSupportFee
     * @return
     */
    @RequestMapping(value = "/marketingSupportFeeDel")
    @ResponseBody
    public Map<String, String> marketingSupportFeeDel(HttpServletRequest request, HttpServletResponse response,
                                                       AgentMarketingSupportFee agentMarketingSupportFee) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            agentMarketingSupportFee.setOrgId(sPrivData.getOrgId());
            agentMarketingSupportFee.setOpId(sPrivData.getOpId());
            agentServiceFeeService.delAgentMarketingSupportFee(agentMarketingSupportFee);
            reMap.put("status", "0");
            reMap.put("message", "删除成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }

    /**
     * 第三方行销项目营销支撑费考核编辑
     * @param request
     * @param response
     * @param agentMarketingSupportFee
     * @return
     */
    @RequestMapping(value = "/marketingSupportFeeEdit")
    @ResponseBody
    public Map<String, String> marketingSupportFeeEdit(HttpServletRequest request, HttpServletResponse response,
                                                        AgentMarketingSupportFee agentMarketingSupportFee) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            agentMarketingSupportFee.setOrgId(sPrivData.getOrgId());
            agentMarketingSupportFee.setOpId(sPrivData.getOpId());
            agentServiceFeeService.editAgentMarketingSupportFee(agentMarketingSupportFee);
            reMap.put("status", "0");
            reMap.put("message", "保存成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }

    /**
     * 获取登入员信息
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("/getOrganizationId")
    @ResponseBody
    public Map<String,Long>getOrganizationId(HttpServletRequest request, HttpServletResponse response){
        Map<String,Long> reMap = new HashMap();
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        reMap.put("orgId",sPrivData.getOrgId());
        return reMap;
    }

    @RequestMapping(value = "/page/businessThirdSupportFeeAdd")
    public String businessThirdSupportFeeAddPag() {
        return "award/businessThirdSupportFeeAdd";
    }

    @RequestMapping(value = "/page/businessThirdSupportFeeManage")
    public String businessThirdSupportFeeManagePag() {
        return "award/businessThirdSupportFeeManage";
    }

    /**
     * 体验营业厅第三方人员支撑项目营销支撑费考核信息
     * @param request
     * @param response
     * @param businessThirdSupportFee
     * @param page
     * @return
     */
    @RequestMapping("/businessThirdSupportFeeQuery")
    @ResponseBody
     public PageData<BusinessThirdSupportFee> businessThirdSupportFeeQuery(HttpServletRequest request,HttpServletResponse response,
                                                                           BusinessThirdSupportFee businessThirdSupportFee,PageParameter page){
            PageData<BusinessThirdSupportFee> pageData = null;
            try{
                pageData = agentServiceFeeService.queryBusinessThirdSupportFee(businessThirdSupportFee,page);
            }catch (Exception e){
                pageData = new PageData<BusinessThirdSupportFee>(new ArrayList<BusinessThirdSupportFee>(),0);
                logger.error("查询失败",e);
            }
            return pageData;
     }

    /**
     * 体验营业厅第三方人员支撑项目营销支撑费考核增加
     * @param request
     * @param response
     * @param businessThirdSupportFee
     * @return
     */
    @RequestMapping("/businessThirdSupportFeeAdd")
    @ResponseBody
     public Map<String ,String> businessThirdSupportFeeAdd(HttpServletRequest request,HttpServletResponse response,
                                                           BusinessThirdSupportFee businessThirdSupportFee){
         Map<String, String> reMap = new HashMap<String, String>();
         try{
             SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
             businessThirdSupportFee.setOrgId(sPrivData.getOrgId());
             businessThirdSupportFee.setOpId(sPrivData.getOpId());
             agentServiceFeeService.addBusinessThirdSupportFee(businessThirdSupportFee);
             reMap.put("status","0");
             reMap.put("message","保存成功！");

         }catch (Exception e){
             reMap.put("status","1");
             reMap.put("message",e.getMessage());
             logger.error("",e);
         }
         return reMap;
     }

    /**
     * 体验营业厅第三方人员支撑项目营销支撑费考核删除
     * @param request
     * @param response
     * @param businessThirdSupportFee
     * @return
     */
     @RequestMapping("/businessThirdSupportFeeDel")
     @ResponseBody
     public Map<String,String> businessThirdSupportFeeDel(HttpServletRequest request,HttpServletResponse response,
                                                          BusinessThirdSupportFee businessThirdSupportFee){
         HashMap<String, String> reMap = new HashMap<String, String>();
         try{
             SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
             businessThirdSupportFee.setOpId(sPrivData.getOpId());
             businessThirdSupportFee.setOrgId(sPrivData.getOrgId());
             agentServiceFeeService.delBusinessThirdSupportFee(businessThirdSupportFee);
             reMap.put("status","0");
             reMap.put("message","删除成功");
         }catch (Exception e){
             reMap.put("status","1");
             reMap.put("message",e.getMessage());
             logger.error("查询失败",e);
         }
         return reMap;
     }

    /**
     * 体验营业厅第三方人员支撑项目营销支撑费考核编辑
     * @param request
     * @param response
     * @param businessThirdSupportFee
     * @return
     */
     @RequestMapping("/businessThirdSupportFeeEdit")
     @ResponseBody
     public Map<String,String> businessThirdSupportFeeEdit(HttpServletRequest request,HttpServletResponse response,
                                                           BusinessThirdSupportFee businessThirdSupportFee){
         Map<String,String> reMap = new HashMap<String, String>();
         try{
             SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
             businessThirdSupportFee.setOrgId(sPrivData.getOrgId());
             businessThirdSupportFee.setOpId(sPrivData.getOpId());
             agentServiceFeeService.editBusinessThirdSupportFee(businessThirdSupportFee);
             reMap.put("status","0");
             reMap.put("message","保存成功");
         }catch (Exception e){
             reMap.put("status","1");
             reMap.put("message",e.getMessage());
             logger.error("",e);
         }
         return reMap;
     }

    @RequestMapping(value = "/page/businessThirdProjectRatioAdd")
    public String businessThirdProjectRatioAdd() {
        return "award/businessThirdProjectRatioAdd";
    }

    @RequestMapping(value = "/page/businessThirdProjectRatioManage")
    public String businessThirdProjectRatioManage() {
        return "award/businessThirdProjectRatioManage";
    }

    @RequestMapping(value = "/page/businessThirdProjectReachManage")
    public String businessThirdProjectReachManage() {
        return "award/businessThirdProjectReachManage";
    }

    @RequestMapping(value = "/page/channelFuseItem")
    public String channelFuseItem() {
        return "award/channelFuseItem";
    }

    @RequestMapping(value = "/page/channelFuseedRigidTarget")
    public String channelFuseedRigidTarget() {
        return "award/channelFuseedRigidTarget";
    }


    @RequestMapping(value = "/page/businessThirdAssessmentAdd")
    public String businessThirdAssessmentAdd() {
        return "award/businessThirdAssessmentAdd";
    }

    @RequestMapping(value = "/page/businessThirdPersonMarket")
    public String businessThirdPersonMarket() {
        return "award/businessThirdPersonMarket";
    }

    @RequestMapping(value = "/page/businessThirdAssessmentManage")
    public String businessThirdAssessmentManage() {
        return "award/businessThirdAssessmentManage";
    }
    /**
     * 第三方行销项目月度目标系数录入
     * @param request
     * @param response
     * @param businessThirdProjectRatio
     * @return
     */
    @RequestMapping(value = "/businessThirdProjectRatioInfoAdd")
    @ResponseBody
    public Map<String, String> businessThirdProjectRatioInfoAdd(HttpServletRequest request, HttpServletResponse response,
                                                            BusinessThirdProjectRatio businessThirdProjectRatio) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if(sPrivData != null){
                if (sPrivData.getOrgId()!=6){
                    logger.error("非市场部账号不允许保存！！");
                    reMap.put("status", "1");
                    reMap.put("message", "非市场部账号不允许保存");
                    throw new Exception("非市场部账号不允许保存！！");
                }
                businessThirdProjectRatio.setOrgId(sPrivData.getOrgId());
                businessThirdProjectRatio.setOpId(sPrivData.getOpId());
            }else {
                logger.error("登录信息不存在！");
                throw new Exception("登录信息不存在！");
            }

            agentServiceFeeService.addBusinessThirdProjectRatio(businessThirdProjectRatio);
            reMap.put("status", "0");
            reMap.put("message", "保存成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }

    /**
     * 第三方行销项目月度目标系数信息
     * @param request
     * @param response
     * @param businessThirdProjectRatio
     * @param page
     * @return
     */
    @RequestMapping(value = "/businessThirdProjectRatioQuery")
    @ResponseBody
    public PageData<BusinessThirdProjectRatio> businessThirdProjectRatioQuery(HttpServletRequest request, HttpServletResponse response,
                                                                       BusinessThirdProjectRatio businessThirdProjectRatio, PageParameter page) {
        PageData<BusinessThirdProjectRatio> pageData = null;
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if(sPrivData == null){
                logger.error("登录信息不存在！");
                throw new Exception("登录信息不存在！");
            }
            pageData = agentServiceFeeService.queryBusinessThirdProjectRatio(businessThirdProjectRatio, page);
        } catch (Exception e) {
            pageData = new PageData<BusinessThirdProjectRatio>(new ArrayList<BusinessThirdProjectRatio>(), 0);
            logger.error("查询失败", e);
        }
        return pageData;
    }

    /**
     * 第三方行销项目月度目标系数删除
     * @param request
     * @param response
     * @param businessThirdProjectRatio
     * @return
     */
    @RequestMapping(value = "/businessThirdProjectRatioDel")
    @ResponseBody
    public Map<String, String> businessThirdProjectRatioDel(HttpServletRequest request, HttpServletResponse response,
                                                      BusinessThirdProjectRatio businessThirdProjectRatio) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if (sPrivData != null){
                businessThirdProjectRatio.setOrgId(sPrivData.getOrgId());
                businessThirdProjectRatio.setOpId(sPrivData.getOpId());
            }else {
                logger.error("登录信息不存在！");
                throw new Exception("登录信息不存在！");
            }
            agentServiceFeeService.delBusinessThirdProjectRatio(businessThirdProjectRatio);
            reMap.put("status", "0");
            reMap.put("message", "删除成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }

    /**
     * 第三方行销项目月度目标系数编辑
     * @param request
     * @param response
     * @param businessThirdProjectRatio
     * @return
     */
    @RequestMapping(value = "/businessThirdProjectRatioEdit")
    @ResponseBody
    public Map<String, String> businessThirdProjectRatioEdit(HttpServletRequest request, HttpServletResponse response,
                                                       BusinessThirdProjectRatio businessThirdProjectRatio) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if (sPrivData != null){
                businessThirdProjectRatio.setOrgId(sPrivData.getOrgId());
                businessThirdProjectRatio.setOpId(sPrivData.getOpId());
            }else {
                logger.error("登录信息不存在！");
                throw new Exception("登录信息不存在！");
            }
            agentServiceFeeService.editBusinessThirdProjectRatio(businessThirdProjectRatio);
            reMap.put("status", "0");
            reMap.put("message", "保存成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }
    /**
     * 第三方行销项目达量系数信息查询
     * @param request
     * @param response
     * @param businessThirdProjectReach
     * @param page
     * @return
     */
    @RequestMapping(value = "/businessThirdProjectReachQuery")
    @ResponseBody
    public PageData<BusinessThirdProjectReach> businessThirdProjectReachQuery(HttpServletRequest request, HttpServletResponse response,
                                                                              BusinessThirdProjectReach businessThirdProjectReach, PageParameter page) {
        PageData<BusinessThirdProjectReach> pageData = null;
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if (sPrivData == null){
                logger.error("登录信息不存在！");
                throw new Exception("登录信息不存在！");
            }else{
                if (sPrivData.getOrgId()!=6){
                    logger.error("非市场部账号不允许查询！！");
                    throw new Exception("非市场部账号不允许查询！！");
                }
            }
            businessThirdProjectReach.setRecStatus(1);
            pageData = agentServiceFeeService.queryBusinessThirdProjectReach(businessThirdProjectReach, page);
        } catch (Exception e) {
            pageData = new PageData<BusinessThirdProjectReach>(new ArrayList<BusinessThirdProjectReach>(), 0);
            logger.error("查询失败", e);
        }
        return pageData;
    }

    /**
     * 第三方行销项目达量系数编辑
     * @param request
     * @param response
     * @param businessThirdProjectReach
     * @return
     */
    @RequestMapping(value = "/businessThirdProjectReachEdit")
    @ResponseBody
    public Map<String, String> businessThirdProjectReachEdit(HttpServletRequest request, HttpServletResponse response,
                                                             BusinessThirdProjectReach businessThirdProjectReach) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if (sPrivData != null){
                businessThirdProjectReach.setOrgId(sPrivData.getOrgId());
                businessThirdProjectReach.setOpId(sPrivData.getOpId());
            }else {
                logger.error("登录信息不存在！");
                throw new Exception("登录信息不存在！");
            }
            agentServiceFeeService.editBusinessThirdProjectReach(businessThirdProjectReach);
            reMap.put("status", "0");
            reMap.put("message", "保存成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }

    @RequestMapping(value = "/channelFuseItemInfoBatchAdd")
    @ResponseBody
    public Map<String, String> channelFuseItemInfoBatchAdd(HttpServletRequest request, HttpServletResponse response,
                                                              @RequestParam(value = "fileName", required = false) String fileName,
                                                              @RequestParam MultipartFile[] files,
                                                              @RequestParam(value = "remoteFileName", required = false) String remoteFileName,
                                                              @RequestParam(value = "remotePath", required = false) String remotePath) throws Exception {
        Map<String, String> resultMap = new HashMap<String, String>();
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        if (sPrivData == null){
            logger.error("登录信息不存在！");
            throw new Exception("登录信息不存在！");
        }
        try {
            fileName = java.net.URLDecoder.decode(URLDecoder.decode(fileName, "UTF-8"), "UTF-8");
            remoteFileName = java.net.URLDecoder.decode(URLDecoder.decode(remoteFileName, "UTF-8"), "UTF-8");
            remotePath = java.net.URLDecoder.decode(URLDecoder.decode(remotePath, "UTF-8"), "UTF-8");
            if (StringUtils.isBlank(fileName)) {
                throw new Exception("请选择要上传的文件!");
            } else if (!"XLSX".equals(fileName.substring(fileName.indexOf(".") + 1).toUpperCase())) {
                throw new Exception("请选择XLSX格式文件!");
            }
            for (MultipartFile mulFile : files) {
                List<List<List<Object>>> data = ExcelUtil.readExcelWithHeader(2, mulFile.getInputStream());
                if (data.size() <= 0) {
                    throw new Exception("文件无有效记录！");
                }
                List<List<Object>> returnList = agentServiceFeeService.channelFuseItemInfo(data.get(0), remoteFileName, remotePath, sPrivData);
                request.getSession().removeAttribute("channelFuseItemInfoBatchAdd");
                request.getSession().setAttribute("channelFuseItemInfoUploadInfo", returnList);
            }
            resultMap.put("status", "0");
        } catch (Exception e) {
            resultMap.put("status", "1");
            resultMap.put("message", e.getMessage());
            logger.error("批量导入失败！", e);
        }
        return resultMap;
    }

    @RequestMapping(value = "/channelFuseItemInfoBatchAddResultExport")
    public void channelFuseItemInfoBatchAddResultExport(
            HttpServletRequest request, HttpServletResponse response) throws Exception {
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        if (sPrivData == null){
            logger.error("登录信息不存在！");
            throw new Exception("登录信息不存在！");
        }
        String fileName = "熔断名单信息批量录入结果";
        try {
            fileName = URLEncoder.encode(fileName, "UTF-8") + ".xls";
        } catch (UnsupportedEncodingException e1) {
            logger.error("", e1);
        }
        response.setHeader("Cache-Control", "no-cache");
        response.setCharacterEncoding("UTF-8");
        response.addHeader("expires", "0");
        response.reset();
        response.resetBuffer();
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        String agent = request.getHeader("USER-AGENT");
        if (agent != null && agent.indexOf("Firefox") != -1) {
            response.addHeader("Content-Disposition", "attachment; filename*=\"utf8''" + fileName + "\"");
        } else {
            response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
        }
        try {
            OutputStream outputStream = response.getOutputStream();
            List<List<Object>> returnList = new ArrayList<List<Object>>();
            if (request.getSession().getAttribute("channelFuseItemInfoUploadInfo") != null) {
                returnList = (List<List<Object>>) request.getSession().getAttribute("channelFuseItemInfoUploadInfo");
            }
            List<String> columns = new ArrayList<String>();
            if (returnList.size() > 0) {
                for (Object obj : returnList.get(0)) {
                    columns.add(obj == null ? "" : obj.toString());
                }
            } else {
                columns.add("当前无可导出数据！");
            }
            if (returnList.size() > 1) {
                returnList.remove(0);
            }
            ExcelUtil.exportExcel("2", "明细", columns, returnList, outputStream);

            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            logger.error("", e);
        }
    }

    @RequestMapping(value = "/channelFuseedRigidTargetBatchAdd")
    @ResponseBody
    public Map<String, String> channelFuseedRigidTargetBatchAdd(HttpServletRequest request, HttpServletResponse response,
                                                           @RequestParam(value = "fileName", required = false) String fileName,
                                                           @RequestParam MultipartFile[] files) throws Exception {
        Map<String, String> resultMap = new HashMap<String, String>();
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        if (sPrivData == null){
            logger.error("登录信息不存在！");
            throw new Exception("登录信息不存在！");
        }
        try {
            fileName = java.net.URLDecoder.decode(URLDecoder.decode(fileName, "UTF-8"), "UTF-8");
            if (StringUtils.isBlank(fileName)) {
                throw new Exception("请选择要上传的文件!");
            } else if (!"XLSX".equals(fileName.substring(fileName.indexOf(".") + 1).toUpperCase())) {
                throw new Exception("请选择XLSX格式文件!");
            }
            for (MultipartFile mulFile : files) {
                List<List<List<Object>>> data = ExcelUtil.readExcelWithHeader(2, mulFile.getInputStream());
                if (data.size() <= 0) {
                    throw new Exception("文件无有效记录！");
                }
                List<List<Object>> returnList = agentServiceFeeService.channelFuseedRigidTargetInfo(data.get(0), sPrivData);
                request.getSession().removeAttribute("channelFuseedRigidTargetBatchAdd");
                request.getSession().setAttribute("channelFuseedRigidTargetUploadInfo", returnList);
            }
            resultMap.put("status", "0");
        } catch (Exception e) {
            resultMap.put("status", "1");
            resultMap.put("message", e.getMessage());
            logger.error("批量导入失败！", e);
        }
        return resultMap;
    }

    @RequestMapping(value = "/channelFuseedRigidTargetBatchAddResultExport")
    public void channelFuseedRigidTargetBatchAddResultExport(
            HttpServletRequest request, HttpServletResponse response) throws Exception {
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        if (sPrivData == null){
            logger.error("登录信息不存在！");
            throw new Exception("登录信息不存在！");
        }
        String fileName = "熔断名单信息批量录入结果";
        try {
            fileName = URLEncoder.encode(fileName, "UTF-8") + ".xls";
        } catch (UnsupportedEncodingException e1) {
            logger.error("", e1);
        }
        response.setHeader("Cache-Control", "no-cache");
        response.setCharacterEncoding("UTF-8");
        response.addHeader("expires", "0");
        response.reset();
        response.resetBuffer();
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        String agent = request.getHeader("USER-AGENT");
        if (agent != null && agent.indexOf("Firefox") != -1) {
            response.addHeader("Content-Disposition", "attachment; filename*=\"utf8''" + fileName + "\"");
        } else {
            response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
        }
        try {
            OutputStream outputStream = response.getOutputStream();
            List<List<Object>> returnList = new ArrayList<List<Object>>();
            if (request.getSession().getAttribute("channelFuseedRigidTargetUploadInfo") != null) {
                returnList = (List<List<Object>>) request.getSession().getAttribute("channelFuseedRigidTargetUploadInfo");
            }
            List<String> columns = new ArrayList<String>();
            if (returnList.size() > 0) {
                for (Object obj : returnList.get(0)) {
                    columns.add(obj == null ? "" : obj.toString());
                }
            } else {
                columns.add("当前无可导出数据！");
            }
            if (returnList.size() > 1) {
                returnList.remove(0);
            }
            ExcelUtil.exportExcel("2", "明细", columns, returnList, outputStream);

            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            logger.error("", e);
        }
    }

    @RequestMapping(value = "/channelFuseItemInfoQuery")
    @ResponseBody
    public PageData<ChannelFuseItem> channelFuseItemInfoQuery(HttpServletRequest request, HttpServletResponse response,
                                                                        ChannelFuseItem channelFuseItem, PageParameter page) throws Exception {
        PageData<ChannelFuseItem> pageData = null;
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        if (sPrivData == null){
            logger.error("登录信息不存在！");
            throw new Exception("登录信息不存在！");
        }
        if (sPrivData.getOrgId() != 6){
            channelFuseItem.setOrgId(sPrivData.getOrgId());
        }
        try {
            channelFuseItem.setChannelEntityName(URLDecoder.decode(URLDecoder.decode(channelFuseItem.getChannelEntityName(), "UTF-8"), "UTF-8"));
            pageData = agentServiceFeeService.queryChannelFuseItemInfo(channelFuseItem, page);
        } catch (Exception e) {
            pageData = new PageData<ChannelFuseItem>(new ArrayList<ChannelFuseItem>(), 0);
            logger.error("查询失败", e);
        }
        return pageData;
    }


    @RequestMapping(value = "/channelFuseItemInfoDel")
    @ResponseBody
    public Map<String, String> channelFuseItemInfoDel(HttpServletRequest request, HttpServletResponse response,
                                                         ChannelFuseItem channelFuseItem) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if (sPrivData == null){
                logger.error("登录信息不存在！");
                throw new Exception("登录信息不存在！");
            }
            agentServiceFeeService.delChannelFuseItemInfo(channelFuseItem);
            reMap.put("status", "0");
            reMap.put("message", "删除成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }


    @RequestMapping(value = "/channelFuseedRigidTargetQuery")
    @ResponseBody
    public PageData<ChannelFuseedRigidTarget> channelFuseedRigidTargetQuery(HttpServletRequest request, HttpServletResponse response,
                                                              ChannelFuseedRigidTarget channelFuseedRigidTarget, PageParameter page) throws Exception {
        PageData<ChannelFuseedRigidTarget> pageData = null;
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        if (sPrivData == null){
            logger.error("登录信息不存在！");
            throw new Exception("登录信息不存在！");
        }
        if (sPrivData.getOrgId() != 6){
            channelFuseedRigidTarget.setOrgId(sPrivData.getOrgId());
        }
        if (channelFuseedRigidTarget.getOrganizeId() != null){
            if (channelFuseedRigidTarget.getOrganizeId() ==6) {
                channelFuseedRigidTarget.setOrganizeId(null);
            }
        }
        try {
            pageData = agentServiceFeeService.queryChannelFuseedRigidTargetInfo(channelFuseedRigidTarget, page);
        } catch (Exception e) {
            pageData = new PageData<ChannelFuseedRigidTarget>(new ArrayList<ChannelFuseedRigidTarget>(), 0);
            logger.error("查询失败", e);
        }
        return pageData;
    }

    /**
     * 融合刚性目标信息修改
     *
     * @param request
     * @param response
     * @param channelFuseedRigidTarget
     * @return
     */
    @RequestMapping(value = "/channelFuseedRigidTargetEdit")
    @ResponseBody
    public Map<String, String> channelFuseedRigidTargetEdit(HttpServletRequest request, HttpServletResponse response,
                                                     ChannelFuseedRigidTarget channelFuseedRigidTarget) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            channelFuseedRigidTarget.setOpId(sPrivData.getOpId());
            agentServiceFeeService.editChannelFuseedRigidTarget(channelFuseedRigidTarget);
            reMap.put("status", "0");
            reMap.put("message", "保存成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }


    /**
     * 属地第三方直销支撑项目月度考核得分录入
     * @param request
     * @param response
     * @param businessThirdAssessment
     * @return
     */
    @RequestMapping(value = "/businessThirdAssessmentAdd")
    @ResponseBody
    public Map<String, String> businessThirdAssessmentAdd(HttpServletRequest request, HttpServletResponse response,
                                                          BusinessThirdAssessment businessThirdAssessment) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);

            if(sPrivData == null){
                logger.error("登录信息不存在！");
                throw new Exception("登录信息不存在！");
            }
            businessThirdAssessment.setOrgId(sPrivData.getOrgId());
            businessThirdAssessment.setOrgName(sPrivData.getOrgName());
            businessThirdAssessment.setOpId(sPrivData.getOpId());
            businessThirdAssessment.setAuditStatus(0);
            agentServiceFeeService.businessThirdAssessmentAdd(businessThirdAssessment, sPrivData);
            reMap.put("status", "0");
            reMap.put("message", "保存成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }
    /**
     * 第三方行销人员基薪信息查询
     * @param request
     * @param response
     * @param businessThirdPersonMarket
     * @param page
     * @return
     */
    @RequestMapping(value = "/businessThirdPersonMarketQuery")
    @ResponseBody
    public PageData<BusinessThirdPersonMarket> businessThirdPersonMarketQuery(HttpServletRequest request, HttpServletResponse response,
                                                                              BusinessThirdPersonMarket businessThirdPersonMarket, PageParameter page) {
        PageData<BusinessThirdPersonMarket> pageData = null;
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if (sPrivData == null){
                logger.error("登录信息不存在！");
                throw new Exception("登录信息不存在！");
            }else{
                if (sPrivData.getOrgId()!=6){
                    logger.error("非市场部账号不允许查询！！");
                    throw new Exception("非市场部账号不允许查询！！");
                }
            }
            businessThirdPersonMarket.setRecStatus(1);
            pageData = agentServiceFeeService.queryBusinessThirdPersonMarket(businessThirdPersonMarket, page);
        } catch (Exception e) {
            pageData = new PageData<BusinessThirdPersonMarket>(new ArrayList<BusinessThirdPersonMarket>(), 0);
            logger.error("查询失败", e);
        }
        return pageData;
    }

    /**
     * 第三方行销人员基薪编辑
     * @param request
     * @param response
     * @param businessThirdPersonMarket
     * @return
     */
    @RequestMapping(value = "/businessThirdPersonMarketEdit")
    @ResponseBody
    public Map<String, String> businessThirdPersonMarketEdit(HttpServletRequest request, HttpServletResponse response,
                                                             BusinessThirdPersonMarket businessThirdPersonMarket) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if (sPrivData != null){
                businessThirdPersonMarket.setOrgId(sPrivData.getOrgId());
                businessThirdPersonMarket.setOpId(sPrivData.getOpId());
            }else {
                logger.error("登录信息不存在！");
                throw new Exception("登录信息不存在！");
            }
            agentServiceFeeService.editBusinessThirdPersonMarket(businessThirdPersonMarket);
            reMap.put("status", "0");
            reMap.put("message", "保存成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }
    /*
    * 第三方行销人员考核 批量录入
    * 属地第三方直销人员月度考核得分录入
    */
    @RequestMapping(value = "/busiThirdXXAssessInfoBatchAdd")
    @ResponseBody
    public Map<String, String> busiThirdXXAssessInfoBatchAdd(HttpServletRequest request, HttpServletResponse response,
                                                           @RequestParam(value = "fileName", required = false) String fileName,
                                                           @RequestParam(value = "flowThreeAmaderName", required = false) String flowThreeAmaderName,
                                                           @RequestParam(value = "remoteFileName", required = false) String remoteFileName,
                                                           @RequestParam(value = "remotePath", required = false) String remotePath,
                                                           @RequestParam MultipartFile[] files) throws Exception {
        Map<String, String> resultMap = new HashMap<String, String>();
        
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        if (sPrivData == null) {
            throw new Exception("登录信息不存在！");
        }
        
        try {
            fileName = java.net.URLDecoder.decode(URLDecoder.decode(fileName, "UTF-8"), "UTF-8");
            Long agentAdjustUseId = Long.parseLong(java.net.URLDecoder.decode(URLDecoder.decode(flowThreeAmaderName, "UTF-8"), "UTF-8"));
            remoteFileName = java.net.URLDecoder.decode(URLDecoder.decode(remoteFileName, "UTF-8"), "UTF-8");
            remotePath = java.net.URLDecoder.decode(URLDecoder.decode(remotePath, "UTF-8"), "UTF-8");
            
            
            if (StringUtils.isBlank(fileName)) {
                throw new Exception("请选择要上传的文件!");
            } else if (!"XLSX".equals(fileName.substring(fileName.indexOf(".") + 1).toUpperCase())) {
                throw new Exception("请选择XLSX格式文件!");
            }
            
            
            for (MultipartFile mulFile : files) {
                List<List<List<Object>>> data = ExcelUtil.readExcelWithHeader(2, mulFile.getInputStream());
                if (data.size() <= 0) {
                    throw new Exception("文件无有效记录！");
                }
                
                
                List<List<Object>> returnList = agentServiceFeeService.busiThirdXXAssessInfo(data.get(0), agentAdjustUseId, remoteFileName, remotePath, sPrivData);
                request.getSession().removeAttribute("busiThirdXXAssessInfoBatchAdd");
                request.getSession().setAttribute("busiThirdXXAssessInfoUploadInfo", returnList);
            }
            resultMap.put("status", "0");
            logger.error("批量导入成功！");
        } catch (Exception e) {
            resultMap.put("status", "1");
            resultMap.put("message", e.getMessage());
            logger.error("批量导入失败！", e);
        }
        return resultMap;
    }
    /*
     * 第三方行销人员考核 错误文件导出
     */
    @RequestMapping(value = "/busiThirdXXAssessInfoBatchAddResultExport")
    public void busiThirdXXAssessInfoBatchAddResultExport(
            HttpServletRequest request, HttpServletResponse response) throws Exception {
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        if (sPrivData == null){
            logger.error("登录信息不存在！");
            throw new Exception("登录信息不存在！");
        }
        String fileName = "第三方行销人员考核批量录入结果";
        try {
            fileName = URLEncoder.encode(fileName, "UTF-8") + ".xls";
        } catch (UnsupportedEncodingException e1) {
            logger.error("", e1);
        }
        response.setHeader("Cache-Control", "no-cache");
        response.setCharacterEncoding("UTF-8");
        response.addHeader("expires", "0");
        response.reset();
        response.resetBuffer();
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        String agent = request.getHeader("USER-AGENT");
        if (agent != null && agent.indexOf("Firefox") != -1) {
            response.addHeader("Content-Disposition", "attachment; filename*=\"utf8''" + fileName + "\"");
        } else {
            response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
        }
        try {
            OutputStream outputStream = response.getOutputStream();
            List<List<Object>> returnList = new ArrayList<List<Object>>();
            if (request.getSession().getAttribute("busiThirdXXAssessInfoUploadInfo") != null) {
                returnList = (List<List<Object>>) request.getSession().getAttribute("busiThirdXXAssessInfoUploadInfo");
            }
            List<String> columns = new ArrayList<String>();
            if (returnList.size() > 0) {
                for (Object obj : returnList.get(0)) {
                    columns.add(obj == null ? "" : obj.toString());
                }
            } else {
                columns.add("当前无可导出数据！");
            }
            if (returnList.size() > 1) {
                returnList.remove(0);
            }
            ExcelUtil.exportExcel("2", "明细", columns, returnList, outputStream);

            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            logger.error("", e);
        }
    }
    /*
     * 第三方行销人员考核管理 查询
     */
    @RequestMapping(value = "/busiThirdXXAssessInfoQuery")
    @ResponseBody
    public PageData<BusiThirdXXAssess> busiThirdXXAssessInfoQuery(HttpServletRequest request,
                                                                  @RequestParam("billMonth") String billMonth,
                                                                  @RequestParam("recStatusList") String recStatusList,
                                                                  HttpServletResponse response,
                                                                  PageParameter page) throws Exception {
        PageData<BusiThirdXXAssess> pageData;
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        if (sPrivData == null){
            throw new Exception("登录信息不存在！");
        }
        BusiThirdXXAssess busiThirdXXAssess = new BusiThirdXXAssess();
        busiThirdXXAssess.setBillMonth(billMonth);
        busiThirdXXAssess.setRecStatusList(recStatusList);
        if (sPrivData.getOrgId() != 6){
            busiThirdXXAssess.setOrgId(sPrivData.getOrgId());
        }
        busiThirdXXAssess.setUserName(sPrivData.getUserName());
        
        try {
            pageData = agentServiceFeeService.busiThirdXXAssessInfoQuery(busiThirdXXAssess, page);
        } catch (Exception e) {
            pageData = new PageData<BusiThirdXXAssess>(new ArrayList<BusiThirdXXAssess>(), 0);
            logger.error("查询失败", e);
        }
        return pageData;
    }


    /*
     * 第三方行销费用考核管理 查询
     */
    @RequestMapping(value = "/businessThirdAssessmentQuery")
    @ResponseBody
    public PageData<BusinessThirdAssessment> businessThirdAssessmentQuery(HttpServletRequest request, HttpServletResponse response,
                                                                  BusinessThirdAssessment businessThirdAssessment, PageParameter page) throws Exception {
        PageData<BusinessThirdAssessment> pageData = null;
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        if (sPrivData == null){
            logger.error("登录信息不存在！");
            throw new Exception("登录信息不存在！");
        }
        try {
            if (sPrivData.getOrgId() != 6){
                businessThirdAssessment.setOrgId(sPrivData.getOrgId());
                businessThirdAssessment.setUserName(sPrivData.getUserName());
            }
            
            if (businessThirdAssessment.getBillMonth() != null) {
                businessThirdAssessment.setBillMonth(businessThirdAssessment.getBillMonth());
            }
            pageData = agentServiceFeeService.businessThirdAssessmentQuery(businessThirdAssessment, page);
        } catch (Exception e) {
            pageData = new PageData<BusinessThirdAssessment>(new ArrayList<BusinessThirdAssessment>(), 0);
            logger.error("查询失败", e);
        }
        return pageData;
    }

    /**
     * 第三方行销人员考核管理 信息修改
     * @param request
     * @param response
     * @param busiThirdXXAssess
     * @return
     */
    @RequestMapping(value = "/busiThirdXXAssessEdit")
    @ResponseBody
    public Map<String, String> busiThirdXXAssessEdit(HttpServletRequest request, HttpServletResponse response,
                                                     BusiThirdXXAssess busiThirdXXAssess) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if (sPrivData != null){
                busiThirdXXAssess.setOrgId(sPrivData.getOrgId());
                busiThirdXXAssess.setOpId(sPrivData.getOpId());
                busiThirdXXAssess.setOrgName(sPrivData.getOrgName());
            }else {
                logger.error("登录信息不存在！");
                throw new Exception("登录信息不存在！");
            }
            agentServiceFeeService.busiThirdXXAssessEdit(busiThirdXXAssess,sPrivData);
            reMap.put("status", "0");
            reMap.put("message", "保存成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }

    /**
     * 第三方行销费用考核管理 信息修改
     * @param request
     * @param response
     * @param businessThirdAssessment
     * @return
     */
    @RequestMapping(value = "/businessThirdAssessmentEdit")
    @ResponseBody
    public Map<String, String> businessThirdAssessmentEdit(HttpServletRequest request, HttpServletResponse response,
                                                           BusinessThirdAssessment businessThirdAssessment) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if (sPrivData != null){
                businessThirdAssessment.setOrgId(sPrivData.getOrgId());
                businessThirdAssessment.setOpId(sPrivData.getOpId());
            }else {
                logger.error("登录信息不存在！");
                throw new Exception("登录信息不存在！");
            }
            agentServiceFeeService.businessThirdAssessmentEdit(businessThirdAssessment,sPrivData);
            reMap.put("status", "0");
            reMap.put("message", "保存成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }

    /**
     * 属地第三方直销费用调整录入
     */
    @RequestMapping(value = "/page/threeNodeNameFeeAdd")
    public String threeNodeNameFeeAdd() {
        return "award/threeNodeNameFeeAdd";
    }

    /*
     * 第三方行销人员费用录入 批量录入
     * 属地第三方直销费用调整录入
     */
    @RequestMapping(value = "/threeNodeNameFeeAdd")
    @ResponseBody
    public Map<String, String> threeNodeNameFeeAdd(HttpServletRequest request, HttpServletResponse response,
                                                   @RequestParam(value = "fileName", required = false) String fileName,
                                                   @RequestParam(value = "flowThreeAmaderName", required = false) String flowThreeAmaderName,
                                                   @RequestParam(value = "remoteFileName", required = false) String remoteFileName,
                                                   @RequestParam(value = "remotePath", required = false) String remotePath,
                                                   @RequestParam MultipartFile[] files) throws Exception {
        Map<String, String> resultMap = new HashMap<String, String>();
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        if (sPrivData == null){
            logger.error("登录信息不存在！");
            throw new Exception("登录信息不存在！");
        }
        try {
            fileName = java.net.URLDecoder.decode(URLDecoder.decode(fileName, "UTF-8"), "UTF-8");
            Long agentAdjustUseId = Long.parseLong(java.net.URLDecoder.decode(URLDecoder.decode(flowThreeAmaderName, "UTF-8"), "UTF-8"));
            remoteFileName = java.net.URLDecoder.decode(URLDecoder.decode(remoteFileName, "UTF-8"), "UTF-8");
            remotePath = java.net.URLDecoder.decode(URLDecoder.decode(remotePath, "UTF-8"), "UTF-8");
            if (StringUtils.isBlank(fileName)) {
                throw new Exception("请选择要上传的文件!");
            } else if (!"XLSX".equals(fileName.substring(fileName.indexOf(".") + 1).toUpperCase())) {
                throw new Exception("请选择XLSX格式文件!");
            }
            for (MultipartFile mulFile : files) {
                List<List<List<Object>>> data = ExcelUtil.readExcelWithHeader(2, mulFile.getInputStream());
                if (data.size() <= 0) {
                    throw new Exception("文件无有效记录！");
                }
                List<List<Object>> returnList = agentServiceFeeService.threeNodeNameFeeBatchAdd(data.get(0),agentAdjustUseId,remoteFileName,remotePath,sPrivData);

                request.getSession().removeAttribute("threeNodeNameFeeBatchAdd");
                request.getSession().setAttribute("threeNodeNameFeeInfoUploadInfo", returnList);
            }
            resultMap.put("status", "0");
            logger.info("批量导入成功！！！");
        } catch (Exception e) {
            resultMap.put("status", "1");
            resultMap.put("message", e.getMessage());
            logger.error("批量导入失败！", e);
        }
        return resultMap;
    }
    /*
     * 第三方行销人员费用管理 错误文件导出
     */
    @RequestMapping(value = "/threeNodeNameFeeInfoBatchAddResultExport")
    public void threeNodeNameFeeInfoBatchAddResultExport(
            HttpServletRequest request, HttpServletResponse response) throws Exception {
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        if (sPrivData == null){
            logger.error("登录信息不存在！");
            throw new Exception("登录信息不存在！");
        }
        String fileName = "第三方行销人员费用批量录入结果";
        try {
            fileName = URLEncoder.encode(fileName, "UTF-8") + ".xls";
        } catch (UnsupportedEncodingException e1) {
            logger.error("", e1);
        }
        response.setHeader("Cache-Control", "no-cache");
        response.setCharacterEncoding("UTF-8");
        response.addHeader("expires", "0");
        response.reset();
        response.resetBuffer();
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        String agent = request.getHeader("USER-AGENT");
        if (agent != null && agent.indexOf("Firefox") != -1) {
            response.addHeader("Content-Disposition", "attachment; filename*=\"utf8''" + fileName + "\"");
        } else {
            response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
        }
        try {
            OutputStream outputStream = response.getOutputStream();
            List<List<Object>> returnList = new ArrayList<List<Object>>();
            if (request.getSession().getAttribute("threeNodeNameFeeInfoUploadInfo") != null) {
                returnList = (List<List<Object>>) request.getSession().getAttribute("threeNodeNameFeeInfoUploadInfo");
            }
            List<String> columns = new ArrayList<String>();
            if (returnList.size() > 0) {
                for (Object obj : returnList.get(0)) {
                    columns.add(obj == null ? "" : obj.toString());
                }
            } else {
                columns.add("当前无可导出数据！");
            }
            if (returnList.size() > 1) {
                returnList.remove(0);
            }
            ExcelUtil.exportExcel("2", "明细", columns, returnList, outputStream);

            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            logger.error("", e);
        }
    }

    /**
     * 第三方行销人员费用管理
     */
    @RequestMapping(value = "/page/threeNodeNameFeeManage")
    public String threeNodeNameFeeManage() {
        return "award/threeNodeNameFeeManage";
    }

    /*
     * 第三方行销人员费用管理 查询
     */
    @RequestMapping(value = "/threeNodeNameFeeInfoQuery")
    @ResponseBody
    public PageData<ThreeNodeNameFee> threeNodeNameFeeInfoQuery(HttpServletRequest request, HttpServletResponse response,
                                                                ThreeNodeNameFee threeNodeNameFee, PageParameter page) throws Exception {
        PageData<ThreeNodeNameFee> pageData = null;
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        if (sPrivData == null){
            logger.error("登录信息不存在！");
            throw new Exception("登录信息不存在！");
        }
        if (sPrivData.getOrgId() != 6){
            threeNodeNameFee.setOrgId(sPrivData.getOrgId());
        }
        try {
            threeNodeNameFee.setUserName(sPrivData.getUserName());
            pageData = agentServiceFeeService.threeNodeNameFeeInfoQuery(threeNodeNameFee, page);
        } catch (Exception e) {
            pageData = new PageData<ThreeNodeNameFee>(new ArrayList<ThreeNodeNameFee>(), 0);
            logger.error("查询失败", e);
        }
        return pageData;
    }
    
    /**
     * 第三方行销人员费用管理 信息修改
     */
    @RequestMapping(value = "/threeNodeNameFeeEdit")
    @ResponseBody
    public Map<String, String> threeNodeNameFeeEdit(HttpServletRequest request, HttpServletResponse response,
                                                    ThreeNodeNameFee threeNodeNameFee) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if (sPrivData != null) {
                threeNodeNameFee.setOrgId(sPrivData.getOrgId());
                threeNodeNameFee.setOpId(sPrivData.getOpId());
                threeNodeNameFee.setOrgName(sPrivData.getOrgName());
            } else {
                logger.error("登录信息不存在！");
                throw new Exception("登录信息不存在！");
            }
            agentServiceFeeService.threeNodeNameFeeEdit(threeNodeNameFee);
            reMap.put("status", "0");
            reMap.put("message", "保存成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }
    @RequestMapping("/checkCompanySettleAdd")
    @ResponseBody
    public Map<String, Object> checkCompanySettleAdd(HttpServletRequest request, HttpServletResponse response,
                                                        @RequestParam(value = "billMonth", required = true) String billMonth) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            CompanySpecSettlement companySpecSettlement = new CompanySpecSettlement();
            companySpecSettlement.setBillMonth(billMonth);
            agentServiceFeeService.checkCompanySettleAdd(companySpecSettlement);
            resultMap.put("status", "0");
        } catch (Exception e) {
            resultMap.put("status", "1");
            resultMap.put("msg", "本月数据已录入，请至在线公司专席结算录入页面进行修改!!");
            logger.error("在线公司专席结算录入失败，失败原因是：" + e);
        }
        return resultMap;
    }


    @RequestMapping("/companyFilesUpload")
    @ResponseBody
    public Map<String, Object> companyFilesUpload(HttpServletRequest request, HttpServletResponse response,
                                                  @RequestParam(value = "billMonth", required = true) String billMonth,
                                                  @RequestParam(value = "fileName", required = true) String fileName,
                                                  @RequestParam MultipartFile[] files) {

        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            for (MultipartFile multipartFile : files) {
                if (!multipartFile.isEmpty()) {
                    //服务器IP
                    List<ChannelSysBaseType> channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 0, null, null);
                    String ip = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器用户名
                    channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 1, null, null);
                    String username = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器密码
                    channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 2, null, null);
                    String password = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器文件路径
                    channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 139, null, null);
                    String remotePath = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器文件
                    String remoteFile = "";
                    String remoteFileName = multipartFile.getOriginalFilename();
                    String[] accUrlNames = remoteFileName.split("\\.");
                    if(accUrlNames.length >= 0){
                        remoteFile = remotePath + billMonth + "_" + UUID.randomUUID().toString() + "." + accUrlNames[accUrlNames.length -1];
                    }else{
                        remoteFile = remotePath + billMonth + "_" + UUID.randomUUID().toString();
                    }
                    SecureFileTransferProtocol.upload(ip, username, password, multipartFile.getInputStream(), remoteFile);
                    resultMap.put("remoteFileName", remoteFileName);
                    resultMap.put("remotePath", remoteFile);
                }
            }
            resultMap.put("status", "1");
            resultMap.put("msg", "文件上传成功！");
        } catch (Exception e) {
            resultMap.put("status", "0");
            resultMap.put("msg", "文件上传失败！");
            logger.error("文件上传失败！", e);
        }
        return resultMap;
    }

    @RequestMapping("/companySpecSettlementSave")
    @ResponseBody
    public Map<String, Object> companySpecSettlementSave(HttpServletRequest request, HttpServletResponse response,
                                                    @RequestParam(value = "billMonth", required = true) String billMonth,
                                                    @RequestParam(value = "settlementPeoples", required = true) Integer settlementPeoples,
                                                    @RequestParam(value = "settlementPrices", required = true) Double settlementPrices,
                                                    @RequestParam(value = "settlementRatio", required = true) Double settlementRatio,
                                                    @RequestParam(value = "remoteFileName", required = true) String fileName,
                                                    @RequestParam(value = "remotePath", required = true) String filePath) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if (sPrivData == null) {
                resultMap.put("status", "0");
                resultMap.put("msg", "登陆超时，请重新登陆！");
                return resultMap;
            }
            CompanySpecSettlement companySpecSettlement = new CompanySpecSettlement();
            companySpecSettlement.setBillMonth(billMonth);
            companySpecSettlement.setSettlementPeoples(settlementPeoples);
            companySpecSettlement.setSettlementPrices(settlementPrices);
            companySpecSettlement.setSettlementRatio(settlementRatio);
            companySpecSettlement.setFileName(fileName);
            companySpecSettlement.setFilePath(filePath);
            companySpecSettlement.setOpId(sPrivData.getOpId());
            companySpecSettlement.setOrgId(sPrivData.getOrgId());
            companySpecSettlement.setDoneDate(new Date());
            companySpecSettlement.setRecStatus(1);
            agentServiceFeeService.companySpecSettlementAdd(companySpecSettlement);
            resultMap.put("status", "1");
            resultMap.put("msg", "在线公司专席结算录入成功！");
        } catch (Exception e) {
            resultMap.put("status", "0");
            resultMap.put("msg", "在线公司专席结算录入失败！" + e);
            logger.error("在线公司专席结算录入保存失败！！" + e);
        }
        return resultMap;
    }

    @RequestMapping(value = "/getCompanySpecSettlement")
    @ResponseBody
    public PageData<CompanySpecSettlement> getCompanySpecSettlement(HttpServletRequest request, HttpServletResponse response,
                                                                CompanySpecSettlement companySpecSettlement, PageParameter page) throws Exception {
        PageData<CompanySpecSettlement> pageData = null;
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        if (sPrivData == null){
            logger.error("登录信息不存在！");
            throw new Exception("登录信息不存在！");
        }

        try {
            pageData = agentServiceFeeService.companySpecSettlementQuery(companySpecSettlement, page);
        } catch (Exception e) {
            pageData = new PageData<CompanySpecSettlement>(new ArrayList<CompanySpecSettlement>(), 0);
            logger.error("查询失败", e);
        }
        return pageData;
    }

    @RequestMapping(value = "/companySpecSettlementEdit")
    @ResponseBody
    public Map<String, String>companySpecSettlementEdit(HttpServletRequest request, HttpServletResponse response,
                                                   CompanySpecSettlement companySpecSettlement) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if (sPrivData != null){
                companySpecSettlement.setOrgId(sPrivData.getOrgId());
                companySpecSettlement.setOpId(sPrivData.getOpId());
            }else {
                logger.error("登录信息不存在！");
                throw new Exception("登录信息不存在！");
            }
            agentServiceFeeService.companySpecSettlementEdit(companySpecSettlement);
            reMap.put("status", "1");
            reMap.put("message", "保存成功！");
        } catch (Exception e) {
            reMap.put("status", "0");
            reMap.put("message", e.getMessage());
            logger.error("修改数据失败，失败原因是：", e);
        }
        return reMap;
    }


    @RequestMapping("/threeNodeFilesUpload")
    @ResponseBody
    public Map<String, Object> threeNodeFilesUpload(HttpServletRequest request, HttpServletResponse response,
                                                  @RequestParam(value = "billMonth", required = true) String billMonth,
                                                  @RequestParam(value = "fileName", required = true) String fileName,
                                                  @RequestParam MultipartFile[] filesUp) {

        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            for (MultipartFile multipartFile : filesUp) {
                if (!multipartFile.isEmpty()) {
                    //服务器IP
                    List<ChannelSysBaseType> channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 0, null, null);
                    String ip = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器用户名
                    channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 1, null, null);
                    String username = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器密码
                    channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 2, null, null);
                    String password = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器文件路径
                    channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 141, null, null);
                    String remotePath = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器文件
                    String remoteFile = "";
                    String remoteFileName = multipartFile.getOriginalFilename();
                    String[] accUrlNames = remoteFileName.split("\\.");     //取上传附件后缀
                    if(accUrlNames.length >= 0){
                        remoteFile = remotePath + billMonth + "_" + UUID.randomUUID().toString() + "." + accUrlNames[accUrlNames.length -1];
                    }else{
                        remoteFile = remotePath + billMonth + "_" + UUID.randomUUID().toString();
                    }
                    SecureFileTransferProtocol.upload(ip, username, password, multipartFile.getInputStream(), remoteFile);
                    resultMap.put("remoteFileName", remoteFileName);
                    resultMap.put("remotePath", remoteFile);
                    logger.info("附件上传成功！附件名称：" + remoteFileName + " 附件路径：" + remoteFile);
                }
            }
            resultMap.put("status", "1");
            resultMap.put("msg", "文件上传成功！");
            logger.info("附件上传成功！！");
        } catch (Exception e) {
            resultMap.put("status", "0");
            resultMap.put("msg", "文件上传失败！");
            logger.error("文件上传失败！", e);
        }
        return resultMap;
    }

    @RequestMapping("/busiThirdXXAssessFilesUpload")
    @ResponseBody
    public Map<String, Object> busiThirdXXAssessFilesUpload(HttpServletRequest request, HttpServletResponse response,
                                                    @RequestParam(value = "billMonth", required = true) String billMonth,
                                                    @RequestParam(value = "fileName", required = true) String fileName,
                                                    @RequestParam MultipartFile[] filesUp) {

        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            for (MultipartFile multipartFile : filesUp) {
                if (!multipartFile.isEmpty()) {
                    //服务器IP
                    List<ChannelSysBaseType> channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 0, null, null);
                    String ip = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器用户名
                    channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 1, null, null);
                    String username = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器密码
                    channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 2, null, null);
                    String password = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器文件路径
                    channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 140, null, null);
                    String remotePath = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器文件
                    String remoteFile = "";
                    String remoteFileName = multipartFile.getOriginalFilename();
                    String[] accUrlNames = remoteFileName.split("\\.");     //取上传附件后缀
                    if(accUrlNames.length >= 0){
                        remoteFile = remotePath + billMonth + "_" + UUID.randomUUID().toString() + "." + accUrlNames[accUrlNames.length -1];
                    }else{
                        remoteFile = remotePath + billMonth + "_" + UUID.randomUUID().toString();
                    }
                    SecureFileTransferProtocol.upload(ip, username, password, multipartFile.getInputStream(), remoteFile);
                    resultMap.put("remoteFileName", remoteFileName);
                    resultMap.put("remotePath", remoteFile);
                    logger.info("附件上传成功！附件名称：" + remoteFileName + " 附件路径：" + remoteFile);
                }
            }
            resultMap.put("status", "1");
            resultMap.put("msg", "文件上传成功！");
            logger.info("附件上传成功！！");
        } catch (Exception e) {
            resultMap.put("status", "0");
            resultMap.put("msg", "文件上传失败！");
            logger.error("文件上传失败！", e);
        }
        return resultMap;
    }



    /*
     * 第三方行销人员考核 批量录入
     */
    @RequestMapping(value = "/busiFuseProInfoBatchAdd")
    @ResponseBody
    public Map<String, String> busiFuseProInfoBatchAdd(HttpServletRequest request, HttpServletResponse response,
                                                             @RequestParam(value = "fileName", required = false) String fileName,
                                                             @RequestParam MultipartFile[] files) throws Exception {
        Map<String, String> resultMap = new HashMap<String, String>();
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        if (sPrivData == null){
            logger.error("登录信息不存在！");
            throw new Exception("登录信息不存在！");
        }
        try {
            fileName = java.net.URLDecoder.decode(URLDecoder.decode(fileName, "UTF-8"), "UTF-8");
            if (StringUtils.isBlank(fileName)) {
                throw new Exception("请选择要上传的文件!");
            } else if (!"XLSX".equals(fileName.substring(fileName.indexOf(".") + 1).toUpperCase())) {
                throw new Exception("请选择XLSX格式文件!");
            }
            for (MultipartFile mulFile : files) {
                List<List<List<Object>>> data = ExcelUtil.readExcelWithHeader(2, mulFile.getInputStream());
                if (data.size() <= 0) {
                    throw new Exception("文件无有效记录！");
                }
                List<List<Object>> returnList = agentServiceFeeService.busiFuseProInfo(data.get(0), sPrivData);
                request.getSession().removeAttribute("busiFuseProInfoBatchAdd");
                request.getSession().setAttribute("busiFuseProInfoUploadInfo", returnList);
            }
            resultMap.put("status", "0");
        } catch (Exception e) {
            resultMap.put("status", "1");
            resultMap.put("message", e.getMessage());
            logger.error("批量导入失败！", e);
        }
        return resultMap;
    }
    /*
     * 第三方行销人员考核 错误文件导出
     */
    @RequestMapping(value = "/busiFuseProInfoBatchAddResultExport")
    public void busiFuseProInfoBatchAddResultExport(
            HttpServletRequest request, HttpServletResponse response) throws Exception {
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        if (sPrivData == null){
            logger.error("登录信息不存在！");
            throw new Exception("登录信息不存在！");
        }
        String fileName = "融合业务零产能批量录入结果";
        try {
            fileName = URLEncoder.encode(fileName, "UTF-8") + ".xls";
        } catch (UnsupportedEncodingException e1) {
            logger.error("", e1);
        }
        response.setHeader("Cache-Control", "no-cache");
        response.setCharacterEncoding("UTF-8");
        response.addHeader("expires", "0");
        response.reset();
        response.resetBuffer();
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        String agent = request.getHeader("USER-AGENT");
        if (agent != null && agent.indexOf("Firefox") != -1) {
            response.addHeader("Content-Disposition", "attachment; filename*=\"utf8''" + fileName + "\"");
        } else {
            response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
        }
        try {
            OutputStream outputStream = response.getOutputStream();
            List<List<Object>> returnList = new ArrayList<List<Object>>();
            if (request.getSession().getAttribute("busiFuseProInfoUploadInfo") != null) {
                returnList = (List<List<Object>>) request.getSession().getAttribute("busiFuseProInfoUploadInfo");
            }
            List<String> columns = new ArrayList<String>();
            if (returnList.size() > 0) {
                for (Object obj : returnList.get(0)) {
                    columns.add(obj == null ? "" : obj.toString());
                }
            } else {
                columns.add("当前无可导出数据！");
            }
            if (returnList.size() > 1) {
                returnList.remove(0);
            }
            ExcelUtil.exportExcel("2", "明细", columns, returnList, outputStream);

            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            logger.error("", e);
        }
    }
    /*
     * 第三方行销人员考核管理 查询
     */
    @RequestMapping(value = "/queryBusiFuseProInfo")
    @ResponseBody
    public PageData<BusiFusePro> queryBusiFuseProInfo(HttpServletRequest request, HttpServletResponse response,
                                                                  BusiFusePro busiFusePro, PageParameter page) throws Exception {
        PageData<BusiFusePro> pageData = null;
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        if (sPrivData == null){
            logger.error("登录信息不存在！");
            throw new Exception("登录信息不存在！");
        }
        try {
            busiFusePro.setChannelEntityName(URLDecoder.decode(URLDecoder.decode(busiFusePro.getChannelEntityName(), "UTF-8"), "UTF-8"));
            pageData = agentServiceFeeService.queryBusiFuseProInfo(busiFusePro, page);
        } catch (Exception e) {
            pageData = new PageData<BusiFusePro>(new ArrayList<BusiFusePro>(), 0);
            logger.error("查询失败", e);
        }
        return pageData;
    }

    @RequestMapping(value = "/busiFuseProDel")
    @ResponseBody
    public Map<String, String> busiFuseProDel(HttpServletRequest request, HttpServletResponse response,
                                                      BusiFusePro busiFusePro) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if (sPrivData == null){
                logger.error("登录信息不存在！");
                throw new Exception("登录信息不存在！");
            }
            agentServiceFeeService.delBusiFuseProInfo(busiFusePro);
            reMap.put("status", "0");
            reMap.put("message", "删除成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }


    @RequestMapping("/busiThirdSalesRoleUpdFilesUpload")
    @ResponseBody
    public Map<String, Object> busiThirdSalesRoleUpdFilesUpload(HttpServletRequest request, HttpServletResponse response,
                                                            @RequestParam(value = "billMonth", required = true) String billMonth,
                                                            @RequestParam(value = "fileName", required = true) String fileName,
                                                            @RequestParam MultipartFile[] filesUp) {

        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            for (MultipartFile multipartFile : filesUp) {
                if (!multipartFile.isEmpty()) {
                    //服务器IP
                    List<ChannelSysBaseType> channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 0, null, null);
                    String ip = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器用户名
                    channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 1, null, null);
                    String username = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器密码
                    channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 2, null, null);
                    String password = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器文件路径
                    channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 142, null, null);
                    String remotePath = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器文件
                    String remoteFile = "";
                    String remoteFileName = multipartFile.getOriginalFilename();
                    String[] accUrlNames = remoteFileName.split("\\.");     //取上传附件后缀
                    if(accUrlNames.length >= 0){
                        remoteFile = remotePath + billMonth + "_" + UUID.randomUUID().toString() + "." + accUrlNames[accUrlNames.length -1];
                    }else{
                        remoteFile = remotePath + billMonth + "_" + UUID.randomUUID().toString();
                    }
                    SecureFileTransferProtocol.upload(ip, username, password, multipartFile.getInputStream(), remoteFile);
                    resultMap.put("remoteFileName", remoteFileName);
                    resultMap.put("remotePath", remoteFile);
                    logger.info("附件上传成功！附件名称：" + remoteFileName + " 附件路径：" + remoteFile);
                }
            }
            resultMap.put("status", "1");
            resultMap.put("msg", "文件上传成功！");
            logger.info("附件上传成功！！");
        } catch (Exception e) {
            resultMap.put("status", "0");
            resultMap.put("msg", "文件上传失败！");
            logger.error("文件上传失败！", e);
        }
        return resultMap;
    }


    /*
     * 第三方行销人员考核 批量录入
     */
    @RequestMapping(value = "/busiThirdSalesRoleUpdInfoBatchAdd")
    @ResponseBody
    public Map<String, String> busiThirdSalesRoleUpdInfoBatchAdd(HttpServletRequest request, HttpServletResponse response,
                                                             @RequestParam(value = "fileName", required = false) String fileName,
                                                             //@RequestParam(value = "flowThreeAmaderName", required = false) String flowThreeAmaderName,
                                                             @RequestParam(value = "remoteFileName", required = false) String remoteFileName,
                                                             @RequestParam(value = "remotePath", required = false) String remotePath,
                                                             @RequestParam MultipartFile[] files) throws Exception {
        Map<String, String> resultMap = new HashMap<String, String>();
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        if (sPrivData == null){
            logger.error("登录信息不存在！");
            throw new Exception("登录信息不存在！");
        }
        try {
            fileName = java.net.URLDecoder.decode(URLDecoder.decode(fileName, "UTF-8"), "UTF-8");
            Long agentAdjustUseId = 0L;
//                    Long.parseLong(java.net.URLDecoder.decode(URLDecoder.decode(flowThreeAmaderName, "UTF-8"), "UTF-8"));
            remoteFileName = java.net.URLDecoder.decode(URLDecoder.decode(remoteFileName, "UTF-8"), "UTF-8");
            remotePath = java.net.URLDecoder.decode(URLDecoder.decode(remotePath, "UTF-8"), "UTF-8");
            if (StringUtils.isBlank(fileName)) {
                throw new Exception("请选择要上传的文件!");
            } else if (!"XLSX".equals(fileName.substring(fileName.indexOf(".") + 1).toUpperCase())) {
                throw new Exception("请选择XLSX格式文件!");
            }
            for (MultipartFile mulFile : files) {
                List<List<List<Object>>> data = ExcelUtil.readExcelWithHeader(2, mulFile.getInputStream());
                if (data.size() <= 0) {
                    throw new Exception("文件无有效记录！");
                }
                List<List<Object>> returnList = agentServiceFeeService.busiThirdSalesRoleUpdInfo(data.get(0),agentAdjustUseId,remoteFileName,remotePath,sPrivData);
                request.getSession().removeAttribute("busiThirdSalesRoleUpdInfoBatchAdd");
                request.getSession().setAttribute("busiThirdSalesRoleUpdInfoUploadInfo", returnList);
            }
            resultMap.put("status", "0");
            logger.error("批量导入成功！");
        } catch (Exception e) {
            resultMap.put("status", "1");
            resultMap.put("message", e.getMessage());
            logger.error("批量导入失败！", e);
        }
        return resultMap;
    }
    /*
     * 第三方行销人员考核 错误文件导出
     */
    @RequestMapping(value = "/busiThirdSalesRoleUpdInfoBatchAddResultExport")
    public void busiThirdSalesRoleUpdInfoBatchAddResultExport(
            HttpServletRequest request, HttpServletResponse response) throws Exception {
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        if (sPrivData == null){
            logger.error("登录信息不存在！");
            throw new Exception("登录信息不存在！");
        }
        String fileName = "第三方直销角色调整批量录入结果";
        try {
            fileName = URLEncoder.encode(fileName, "UTF-8") + ".xls";
        } catch (UnsupportedEncodingException e1) {
            logger.error("", e1);
        }
        response.setHeader("Cache-Control", "no-cache");
        response.setCharacterEncoding("UTF-8");
        response.addHeader("expires", "0");
        response.reset();
        response.resetBuffer();
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        String agent = request.getHeader("USER-AGENT");
        if (agent != null && agent.indexOf("Firefox") != -1) {
            response.addHeader("Content-Disposition", "attachment; filename*=\"utf8''" + fileName + "\"");
        } else {
            response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
        }
        try {
            OutputStream outputStream = response.getOutputStream();
            List<List<Object>> returnList = new ArrayList<List<Object>>();
            if (request.getSession().getAttribute("busiThirdSalesRoleUpdInfoUploadInfo") != null) {
                returnList = (List<List<Object>>) request.getSession().getAttribute("busiThirdSalesRoleUpdInfoUploadInfo");
            }
            List<String> columns = new ArrayList<String>();
            if (returnList.size() > 0) {
                for (Object obj : returnList.get(0)) {
                    columns.add(obj == null ? "" : obj.toString());
                }
            } else {
                columns.add("当前无可导出数据！");
            }
            if (returnList.size() > 1) {
                returnList.remove(0);
            }
            ExcelUtil.exportExcel("2", "明细", columns, returnList, outputStream);

            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            logger.error("", e);
        }
    }


    /*
     * 第三方直销角色调整查询
     */
    @RequestMapping(value = "/busiThirdSalesRoleUpdInfoQuery")
    @ResponseBody
    public PageData<BusiThirdSalesRoleUpd> busiThirdSalesRoleUpdInfoQuery(HttpServletRequest request, HttpServletResponse response,
                                                                  BusiThirdSalesRoleUpd busiThirdSalesRoleUpd, PageParameter page) throws Exception {
        PageData<BusiThirdSalesRoleUpd> pageData = null;
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        if (sPrivData == null){
            logger.error("登录信息不存在！");
            throw new Exception("登录信息不存在！");
        }
        if (sPrivData.getOrgId() != 6){
            busiThirdSalesRoleUpd.setOrgId(sPrivData.getOrgId());
        }
        busiThirdSalesRoleUpd.setUserName(sPrivData.getUserName());
        if (com.ailk.newchnl.util.StringUtils.isNotNullOrBlank(busiThirdSalesRoleUpd.getNodeName())){
            busiThirdSalesRoleUpd.setNodeName(URLDecoder.decode(URLDecoder.decode(busiThirdSalesRoleUpd.getNodeName(),"UTF-8"),"UTF-8"));
        }
        if (com.ailk.newchnl.util.StringUtils.isNotNullOrBlank(busiThirdSalesRoleUpd.getOrgName())){
            busiThirdSalesRoleUpd.setOrgName(URLDecoder.decode(URLDecoder.decode(busiThirdSalesRoleUpd.getOrgName(),"UTF-8"),"UTF-8"));
        }
        try {
            if (com.ailk.newchnl.util.StringUtils.isNotNullOrBlank(busiThirdSalesRoleUpd.getBillMonth())) {
                busiThirdSalesRoleUpd.setBillMonth(busiThirdSalesRoleUpd.getBillMonth());
            }
            pageData = agentServiceFeeService.busiThirdSalesRoleUpdInfoQuery(busiThirdSalesRoleUpd, page);
        } catch (Exception e) {
            pageData = new PageData<BusiThirdSalesRoleUpd>(new ArrayList<BusiThirdSalesRoleUpd>(), 0);
            logger.error("查询失败", e);
        }
        return pageData;
    }
    @RequestMapping("/channelFuseItemFilesUpload")
    @ResponseBody
    public Map<String, Object> channelFuseItemFilesUpload(HttpServletRequest request, HttpServletResponse response,
                                                    @RequestParam(value = "billMonth", required = true) String billMonth,
                                                    @RequestParam(value = "fileName", required = true) String fileName,
                                                    @RequestParam MultipartFile[] filesUp) {

        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            for (MultipartFile multipartFile : filesUp) {
                if (!multipartFile.isEmpty()) {
                    //服务器IP
                    List<ChannelSysBaseType> channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 0, null, null);
                    String ip = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器用户名
                    channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 1, null, null);
                    String username = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器密码
                    channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 2, null, null);
                    String password = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器文件路径
                    channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 306, null, null);
                    String remotePath = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器文件
                    String remoteFile = "";
                    String remoteFileName = multipartFile.getOriginalFilename();
                    String[] accUrlNames = remoteFileName.split("\\.");     //取上传附件后缀
                    if(accUrlNames.length >= 0){
                        remoteFile = remotePath + billMonth + "_" + UUID.randomUUID().toString() + "." + accUrlNames[accUrlNames.length -1];
                    }else{
                        remoteFile = remotePath + billMonth + "_" + UUID.randomUUID().toString();
                    }
                    SecureFileTransferProtocol.upload(ip, username, password, multipartFile.getInputStream(), remoteFile);
                    resultMap.put("remoteFileName", remoteFileName);
                    resultMap.put("remotePath", remoteFile);
                    logger.info("附件上传成功！附件名称：" + remoteFileName + " 附件路径：" + remoteFile);
                }
            }
            resultMap.put("status", "1");
            resultMap.put("msg", "文件上传成功！");
            logger.info("附件上传成功！！");
        } catch (Exception e) {
            resultMap.put("status", "0");
            resultMap.put("msg", "文件上传失败！");
            logger.error("文件上传失败！", e);
        }
        return resultMap;
    }

    /**
     *营业厅虚拟利润考核录入
     * @return
     */
    @RequestMapping(value = "/page/channelNodeGainAssessAdd")
    public String channelNodeGainAssessAdd() {
        return "award/channelNodeGainAssessAdd";
    }
    /**
     *营业厅虚拟利润考核管理
     * @return
     */
    @RequestMapping(value = "/page/channelNodeGainAssessManage")
    public String channelNodeGainAssessManage() {
        return "award/channelNodeGainAssessManage";
    }

    /**
     * 营业厅虚拟利润考核录入
     * @param request
     * @param response
     * @param channelNodeGainAssessManage
     * @return
     */
    @RequestMapping(value = "/channelNodeGainAssessInfoAdd")
    @ResponseBody
    public Map<String, String> channelNodeGainAssessInfoAdd(HttpServletRequest request, HttpServletResponse response,
                                                                ChannelNodeGainAssessManage channelNodeGainAssessManage) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if(sPrivData != null){
                channelNodeGainAssessManage.setOrgId(sPrivData.getOrgId());
                channelNodeGainAssessManage.setOpId(sPrivData.getOpId());
            }else {
                logger.error("登录信息不存在！");
                throw new Exception("登录信息不存在！");
            }

            agentServiceFeeService.addChannelNodeGainAssessManage(channelNodeGainAssessManage);
            reMap.put("status", "0");
            reMap.put("message", "保存成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }

    /**
     * 营业厅虚拟利润考核信息查询
     * @param request
     * @param response
     * @param channelNodeGainAssessManage
     * @param page
     * @return
     */
    @RequestMapping(value = "/channelNodeGainAssessInfoQuery")
    @ResponseBody
    public PageData<ChannelNodeGainAssessManage> channelNodeGainAssessInfoQuery(HttpServletRequest request, HttpServletResponse response,
                                                                              ChannelNodeGainAssessManage channelNodeGainAssessManage, PageParameter page) {
        PageData<ChannelNodeGainAssessManage> pageData = null;
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if(sPrivData == null){
                logger.error("登录信息不存在！");
                throw new Exception("登录信息不存在！");
            }
            if (sPrivData.getOrgId() != 6){
                channelNodeGainAssessManage.setOrgId(sPrivData.getOrgId());
            }
            channelNodeGainAssessManage.setNodeName(URLDecoder.decode(URLDecoder.decode(channelNodeGainAssessManage.getNodeName(), "UTF-8"), "UTF-8"));
            pageData = agentServiceFeeService.queryChannelNodeGainAssessManage(channelNodeGainAssessManage, page);
        } catch (Exception e) {
            pageData = new PageData<ChannelNodeGainAssessManage>(new ArrayList<ChannelNodeGainAssessManage>(), 0);
            logger.error("查询失败", e);
        }
        return pageData;
    }

    /**
     * 营业厅虚拟利润考核信息 删除
     * @param request
     * @param response
     * @param channelNodeGainAssessManage
     * @return
     */
    @RequestMapping(value = "/channelNodeGainAssessInfoDel")
    @ResponseBody
    public Map<String, String> channelNodeGainAssessInfoDel(HttpServletRequest request, HttpServletResponse response,
                                                            ChannelNodeGainAssessManage channelNodeGainAssessManage) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if (sPrivData != null){
                channelNodeGainAssessManage.setOrgId(sPrivData.getOrgId());
                channelNodeGainAssessManage.setOpId(sPrivData.getOpId());
            }else {
                logger.error("登录信息不存在！");
                throw new Exception("登录信息不存在！");
            }
            agentServiceFeeService.delChannelNodeGainAssessManage(channelNodeGainAssessManage);
            reMap.put("status", "0");
            reMap.put("message", "删除成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }


    @RequestMapping("/dependencyBusiThirdXXJSJLFilesUpload")
    @ResponseBody
    public Map<String, Object> dependencyBusiThirdXXJSJLFilesUpload(HttpServletRequest request, HttpServletResponse response,
                                                            @RequestParam(value = "billMonth", required = true) String billMonth,
                                                            @RequestParam(value = "fileName", required = true) String fileName,
                                                            @RequestParam MultipartFile[] filesUp) throws Exception {

        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if (sPrivData == null){
                logger.error("登录信息不存在！");
                throw new Exception("登录信息不存在！");
            }

            for (MultipartFile multipartFile : filesUp) {
                if (!multipartFile.isEmpty()) {
                    //服务器IP
                    List<ChannelSysBaseType> channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 0, null, null);
                    String ip = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器用户名
                    channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 1, null, null);
                    String username = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器密码
                    channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 2, null, null);
                    String password = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器文件路径
                    channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 140, null, null);
                    String remotePath = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器文件
                    String remoteFile = "";
                    String remoteFileName = multipartFile.getOriginalFilename();
                    String[] accUrlNames = remoteFileName.split("\\.");     //取上传附件后缀
                    if (!SafetyFlewUtils.checkFileUpload(remoteFileName)){
                        throw new Exception("文件上传校验失败！！");
                    }
                    if(accUrlNames.length >= 0){
                        remoteFile = remotePath + billMonth + "_" + UUID.randomUUID().toString() + "." + accUrlNames[accUrlNames.length -1];
                    }else{
                        remoteFile = remotePath + billMonth + "_" + UUID.randomUUID().toString();
                    }
                    SecureFileTransferProtocol.upload(ip, username, password, multipartFile.getInputStream(), remoteFile);
                    resultMap.put("remoteFileName", remoteFileName);
                    resultMap.put("remotePath", remoteFile);
                    logger.info("附件上传成功！附件名称：" + remoteFileName + " 附件路径：" + remoteFile);
                }
            }
            resultMap.put("status", "1");
            resultMap.put("msg", "文件上传成功！");
            logger.info("附件上传成功！！");
        } catch (Exception e) {
            resultMap.put("status", "0");
            resultMap.put("msg", "文件上传失败！");
            logger.error("文件上传失败！", e);
        }
        return resultMap;
    }


    /*
     * 属地第三方行销即时激励 批量录入
     */
    @RequestMapping(value = "/dependencyBusiThirdXXJSJLInfoBatchAdd")
    @ResponseBody
    public Map<String, String> dependencyBusiThirdXXJSJLInfoBatchAdd(HttpServletRequest request, HttpServletResponse response,
                                                             @RequestParam(value = "fileName", required = false) String fileName,
                                                             @RequestParam(value = "remoteFileName", required = false) String remoteFileName,
                                                             @RequestParam(value = "remotePath", required = false) String remotePath,
                                                             @RequestParam(value = "flowThreeAmaderName", required = false) String flowThreeAmaderName,
                                                             @RequestParam MultipartFile[] files) throws Exception {
        Map<String, String> resultMap = new HashMap<String, String>();
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        if (sPrivData == null){
            logger.error("登录信息不存在！");
            throw new Exception("登录信息不存在！");
        }
        try {
            fileName = java.net.URLDecoder.decode(URLDecoder.decode(fileName, "UTF-8"), "UTF-8");
            remoteFileName = java.net.URLDecoder.decode(URLDecoder.decode(remoteFileName, "UTF-8"), "UTF-8");
            remotePath = java.net.URLDecoder.decode(URLDecoder.decode(remotePath, "UTF-8"), "UTF-8");
            Long agentAdjustUseId = Long.parseLong(java.net.URLDecoder.decode(URLDecoder.decode(flowThreeAmaderName, "UTF-8"), "UTF-8"));
            if (StringUtils.isBlank(fileName)) {
                throw new Exception("请选择要上传的文件!");
            } else if (!"XLSX".equals(fileName.substring(fileName.indexOf(".") + 1).toUpperCase())) {
                throw new Exception("请选择XLSX格式文件!");
            }
            for (MultipartFile mulFile : files) {
                List<List<List<Object>>> data = ExcelUtil.readExcelWithHeader(2, mulFile.getInputStream());
                if (data.size() <= 0) {
                    throw new Exception("文件无有效记录！");
                }
                List<List<Object>> returnList = agentServiceFeeService.dependencyBusiThirdXXJSJLInfo(data.get(0),remoteFileName,remotePath,sPrivData,agentAdjustUseId);
                request.getSession().removeAttribute("dependencyBusiThirdXXJSJLInfoBatchAdd");
                request.getSession().setAttribute("dependencyBusiThirdXXJSJLUploadInfo", returnList);
            }
            resultMap.put("status", "0");
            logger.error("批量导入成功！");
        } catch (Exception e) {
            resultMap.put("status", "1");
            resultMap.put("message", e.getMessage());
            logger.error("批量导入失败！", e);
        }
        return resultMap;
    }
    /*
     * 第三方行销人员考核 错误文件导出
     */
    @RequestMapping(value = "/dependencyBusiThirdXXJSJLInfoBatchAddResultExport")
    public void dependencyBusiThirdXXJSJLInfoBatchAddResultExport(
            HttpServletRequest request, HttpServletResponse response) throws Exception {
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        if (sPrivData == null){
            logger.error("登录信息不存在！");
            throw new Exception("登录信息不存在！");
        }
        String fileName = "属地第三方行销即时激励批量录入结果";
        try {
            fileName = URLEncoder.encode(fileName, "UTF-8") + ".xls";
        } catch (UnsupportedEncodingException e1) {
            logger.error("", e1);
        }
        response.setHeader("Cache-Control", "no-cache");
        response.setCharacterEncoding("UTF-8");
        response.addHeader("expires", "0");
        response.reset();
        response.resetBuffer();
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        String agent = request.getHeader("USER-AGENT");
        if (agent != null && agent.indexOf("Firefox") != -1) {
            response.addHeader("Content-Disposition", "attachment; filename*=\"utf8''" + fileName + "\"");
        } else {
            response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
        }
        try {
            OutputStream outputStream = response.getOutputStream();
            List<List<Object>> returnList = new ArrayList<List<Object>>();
            if (request.getSession().getAttribute("dependencyBusiThirdXXJSJLUploadInfo") != null) {
                returnList = (List<List<Object>>) request.getSession().getAttribute("dependencyBusiThirdXXJSJLUploadInfo");
            }
            List<String> columns = new ArrayList<String>();
            if (returnList.size() > 0) {
                for (Object obj : returnList.get(0)) {
                    columns.add(obj == null ? "" : obj.toString());
                }
            } else {
                columns.add("当前无可导出数据！");
            }
            if (returnList.size() > 1) {
                returnList.remove(0);
            }
            ExcelUtil.exportExcel("2", "明细", columns, returnList, outputStream);

            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            logger.error("", e);
        }
    }
    /*
     * 第三方行销人员考核管理 查询
     */
    @RequestMapping(value = "/dependencyBusiThirdXXJSJLInfoQuery")
    @ResponseBody
    public PageData<DependencyBusiThirdXXJSJL> dependencyBusiThirdXXJSJLInfoQuery(HttpServletRequest request, HttpServletResponse response,
                                                                  DependencyBusiThirdXXJSJL dependencyBusiThirdXXJSJL, PageParameter page) throws Exception {
        PageData<DependencyBusiThirdXXJSJL> pageData = null;
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        if (sPrivData == null){
            logger.error("登录信息不存在！");
            throw new Exception("登录信息不存在！");
        }
        if (dependencyBusiThirdXXJSJL.getRecStatus() != null){
            dependencyBusiThirdXXJSJL.setAgentAdjustUseName(sPrivData.getUserName());
        }
        if (dependencyBusiThirdXXJSJL.getOrgId() == null){
            if (sPrivData.getOrgId() != 6){
                dependencyBusiThirdXXJSJL.setOrgId(sPrivData.getOrgId());
            }
        }else {
            if (sPrivData.getOrgId() != 6 && (!sPrivData.getOrgId().equals(dependencyBusiThirdXXJSJL.getOrgId()) && dependencyBusiThirdXXJSJL.getOrgId()!=null)){
                return  pageData = new PageData<DependencyBusiThirdXXJSJL>(new ArrayList<DependencyBusiThirdXXJSJL>(), 0);
            }
        }

        try {
            pageData = agentServiceFeeService.dependencyBusiThirdXXJSJLInfoQuery(dependencyBusiThirdXXJSJL, page);
        } catch (Exception e) {
            pageData = new PageData<DependencyBusiThirdXXJSJL>(new ArrayList<DependencyBusiThirdXXJSJL>(), 0);
            logger.error("查询失败", e);
        }
        return pageData;
    }

    /**
     * 第三方直销反向折算机制时间设置录入
     * @param request
     * @param response
     * @param busiThirdSetDate
     * @return
     */
    @RequestMapping(value = "/busiThirdSetDateInfoAdd")
    @ResponseBody
    public Map<String, String> busiThirdSetDateInfoAdd(HttpServletRequest request, HttpServletResponse response,
                                                            BusiThirdSetDate busiThirdSetDate) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if(sPrivData != null){
                busiThirdSetDate.setOrgId(sPrivData.getOrgId());
                busiThirdSetDate.setOpId(sPrivData.getOpId());
            }else {
                logger.error("登录信息不存在！");
                throw new Exception("登录信息不存在！");
            }
            if (!SafetyFlewUtils.checkFileAttrUpload(busiThirdSetDate.getBillMonth())){
                throw new Exception("参数不合法，请重新输入！！");
            }
            if (!SafetyFlewUtils.checkFileAttrUpload(busiThirdSetDate.getStartDate())){
                throw new Exception("参数不合法，请重新输入！！");
            }
            if (!SafetyFlewUtils.checkFileAttrUpload(busiThirdSetDate.getEndDate())){
                throw new Exception("参数不合法，请重新输入！！");
            }
            agentServiceFeeService.addBusiThirdSetDateInfo(busiThirdSetDate);
            reMap.put("status", "0");
            reMap.put("message", "保存成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }

    /**
     * 第三方直销反向折算机制时间设置查询
     * @param request
     * @param response
     * @param busiThirdSetDate
     * @param page
     * @return
     */
    @RequestMapping(value = "/busiThirdSetDateInfoQuery")
    @ResponseBody
    public PageData<BusiThirdSetDate> busiThirdSetDateInfoQuery(HttpServletRequest request, HttpServletResponse response,
                                                                BusiThirdSetDate busiThirdSetDate, PageParameter page) {
        PageData<BusiThirdSetDate> pageData = null;
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if(sPrivData == null){
                logger.error("登录信息不存在！");
                throw new Exception("登录信息不存在！");
            }
            busiThirdSetDate.setOrgId(sPrivData.getOrgId());
            pageData = agentServiceFeeService.busiThirdSetDateInfoQuery(busiThirdSetDate, page);
        } catch (Exception e) {
            pageData = new PageData<BusiThirdSetDate>(new ArrayList<BusiThirdSetDate>(), 0);
            logger.error("查询失败", e);
        }
        return pageData;
    }



    @RequestMapping(value = "/busiThirdPriceServiceRatioBatchAdd")
    @ResponseBody
    public Map<String, String> busiThirdPriceServiceRatioBatchAdd(HttpServletRequest request, HttpServletResponse response,
                                                                @RequestParam(value = "fileName", required = false) String fileName,
                                                                @RequestParam MultipartFile[] files) throws Exception {
        Map<String, String> resultMap = new HashMap<String, String>();
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        if (sPrivData == null){
            logger.error("登录信息不存在！");
            throw new Exception("登录信息不存在！");
        }
        try {
            fileName = java.net.URLDecoder.decode(URLDecoder.decode(fileName, "UTF-8"), "UTF-8");
            if (StringUtils.isBlank(fileName)) {
                throw new Exception("请选择要上传的文件!");
            } else if (!"XLSX".equals(fileName.substring(fileName.indexOf(".") + 1).toUpperCase())) {
                throw new Exception("请选择XLSX格式文件!");
            }
            for (MultipartFile mulFile : files) {
                List<List<List<Object>>> data = ExcelUtil.readExcelWithHeader(2, mulFile.getInputStream());
                if (data.size() <= 0) {
                    throw new Exception("文件无有效记录！");
                }
                List<List<Object>> returnList = agentServiceFeeService.busiThirdPriceServiceRatioAdd(data.get(0), sPrivData);
                request.getSession().removeAttribute("busiThirdPriceServiceRatioAdd");
                request.getSession().setAttribute("busiThirdPriceServiceRatioUploadInfo", returnList);
            }
            resultMap.put("status", "0");
        } catch (Exception e) {
            resultMap.put("status", "1");
            resultMap.put("message", e.getMessage());
            logger.error("批量导入失败！", e);
        }
        return resultMap;
    }
    @RequestMapping(value = "/busiThirdPriceServiceRatioAddResultExport")
    public void busiThirdPriceServiceRatioAddResultExport(
            HttpServletRequest request, HttpServletResponse response) throws Exception {
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        if (sPrivData == null){
            logger.error("登录信息不存在！");
            throw new Exception("登录信息不存在！");
        }
        String fileName = "属地第三方直销服务单价自定义系数批量录入结果";
        try {
            fileName = URLEncoder.encode(fileName, "UTF-8") + ".xls";
        } catch (UnsupportedEncodingException e1) {
            logger.error("", e1);
        }
        response.setHeader("Cache-Control", "no-cache");
        response.setCharacterEncoding("UTF-8");
        response.addHeader("expires", "0");
        response.reset();
        response.resetBuffer();
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        String agent = request.getHeader("USER-AGENT");
        if (agent != null && agent.indexOf("Firefox") != -1) {
            response.addHeader("Content-Disposition", "attachment; filename*=\"utf8''" + fileName + "\"");
        } else {
            response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
        }
        try {
            OutputStream outputStream = response.getOutputStream();
            List<List<Object>> returnList = new ArrayList<List<Object>>();
            if (request.getSession().getAttribute("busiThirdPriceServiceRatioUploadInfo") != null) {
                returnList = (List<List<Object>>) request.getSession().getAttribute("busiThirdPriceServiceRatioUploadInfo");
            }
            List<String> columns = new ArrayList<String>();
            if (returnList.size() > 0) {
                for (Object obj : returnList.get(0)) {
                    columns.add(obj == null ? "" : obj.toString());
                }
            } else {
                columns.add("当前无可导出数据！");
            }
            if (returnList.size() > 1) {
                returnList.remove(0);
            }
            ExcelUtil.exportExcel("2", "明细", columns, returnList, outputStream);

            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            logger.error("", e);
        }
    }


    @RequestMapping(value = "/busiThirdPriceServiceRatioInfoQuery")
    @ResponseBody
    public PageData<BusiThirdPriceServiceRatio> busiThirdPriceServiceRatioInfoQuery(HttpServletRequest request, HttpServletResponse response,
                                                                BusiThirdPriceServiceRatio busiThirdPriceServiceRatio, PageParameter page) {
        PageData<BusiThirdPriceServiceRatio> pageData = null;
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if(sPrivData == null){
                logger.error("登录信息不存在！");
                throw new Exception("登录信息不存在！");
            }
            if (sPrivData.getOrgId() != 6){
                busiThirdPriceServiceRatio.setOrgId(sPrivData.getOrgId());
            }
            pageData = agentServiceFeeService.busiThirdPriceServiceRatioInfoQuery(busiThirdPriceServiceRatio, page);
        } catch (Exception e) {
            pageData = new PageData<BusiThirdPriceServiceRatio>(new ArrayList<BusiThirdPriceServiceRatio>(), 0);
            logger.error("查询失败", e);
        }
        return pageData;
    }


    @RequestMapping(value = "/busiThirdSpecialOfferInfoBatchAdd")
    @ResponseBody
    public Map<String, String> busiThirdSpecialOfferInfoBatchAdd(HttpServletRequest request, HttpServletResponse response,
                                                                  @RequestParam(value = "fileName", required = false) String fileName,
                                                                  @RequestParam MultipartFile[] files) throws Exception {
        Map<String, String> resultMap = new HashMap<String, String>();
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        if (sPrivData == null){
            logger.error("登录信息不存在！");
            throw new Exception("登录信息不存在！");
        }
        try {
            fileName = java.net.URLDecoder.decode(URLDecoder.decode(fileName, "UTF-8"), "UTF-8");
            if (StringUtils.isBlank(fileName)) {
                throw new Exception("请选择要上传的文件!");
            } else if (!"XLSX".equals(fileName.substring(fileName.indexOf(".") + 1).toUpperCase())) {
                throw new Exception("请选择XLSX格式文件!");
            }
            for (MultipartFile mulFile : files) {
                List<List<List<Object>>> data = ExcelUtil.readExcelWithHeader(2, mulFile.getInputStream());
                if (data.size() <= 0) {
                    throw new Exception("文件无有效记录！");
                }
                List<List<Object>> returnList = agentServiceFeeService.busiThirdSpecialOfferInfoAdd(data.get(0), sPrivData);
                request.getSession().removeAttribute("busiThirdPriceServiceRatioAdd");
                request.getSession().setAttribute("busiThirdSpecialOfferInfoUploadInfo", returnList);
            }
            resultMap.put("status", "0");
        } catch (Exception e) {
            resultMap.put("status", "1");
            resultMap.put("message", e.getMessage());
            logger.error("批量导入失败！", e);
        }
        return resultMap;
    }
    @RequestMapping(value = "/busiThirdSpecialOfferInfoAddResultExport")
    public void busiThirdSpecialOfferInfoAddResultExport(
            HttpServletRequest request, HttpServletResponse response) throws Exception {
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        if (sPrivData == null){
            logger.error("登录信息不存在！");
            throw new Exception("登录信息不存在！");
        }
        String fileName = "集团成员个性化资费录入结果";
        try {
            fileName = URLEncoder.encode(fileName, "UTF-8") + ".xls";
        } catch (UnsupportedEncodingException e1) {
            logger.error("", e1);
        }
        response.setHeader("Cache-Control", "no-cache");
        response.setCharacterEncoding("UTF-8");
        response.addHeader("expires", "0");
        response.reset();
        response.resetBuffer();
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        String agent = request.getHeader("USER-AGENT");
        if (agent != null && agent.indexOf("Firefox") != -1) {
            response.addHeader("Content-Disposition", "attachment; filename*=\"utf8''" + fileName + "\"");
        } else {
            response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
        }
        try {
            OutputStream outputStream = response.getOutputStream();
            List<List<Object>> returnList = new ArrayList<List<Object>>();
            if (request.getSession().getAttribute("busiThirdSpecialOfferInfoUploadInfo") != null) {
                returnList = (List<List<Object>>) request.getSession().getAttribute("busiThirdSpecialOfferInfoUploadInfo");
            }
            List<String> columns = new ArrayList<String>();
            if (returnList.size() > 0) {
                for (Object obj : returnList.get(0)) {
                    columns.add(obj == null ? "" : obj.toString());
                }
            } else {
                columns.add("当前无可导出数据！");
            }
            if (returnList.size() > 1) {
                returnList.remove(0);
            }
            ExcelUtil.exportExcel("2", "明细", columns, returnList, outputStream);

            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            logger.error("", e);
        }
    }


    @RequestMapping(value = "/busiThirdSpecialOfferInfoQuery")
    @ResponseBody
    public PageData<BusiThirdSpecialOfferInfo> busiThirdSpecialOfferInfoQuery(HttpServletRequest request, HttpServletResponse response,
                                                                                    BusiThirdSpecialOfferInfo busiThirdSpecialOfferInfo, PageParameter page) {
        PageData<BusiThirdSpecialOfferInfo> pageData = null;
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if(sPrivData == null){
                logger.error("登录信息不存在！");
                throw new Exception("登录信息不存在！");
            }
            if (sPrivData.getOrgId() != 6){
                busiThirdSpecialOfferInfo.setOrgId(sPrivData.getOrgId());
            }
            pageData = agentServiceFeeService.busiThirdSpecialOfferInfoQuery(busiThirdSpecialOfferInfo, page);
        } catch (Exception e) {
            pageData = new PageData<BusiThirdSpecialOfferInfo>(new ArrayList<BusiThirdSpecialOfferInfo>(), 0);
            logger.error("查询失败", e);
        }
        return pageData;
    }
    
    /**
     * 中移铁通在线导购项目考核打分录入：新增数据
     */
    @RequestMapping("/addTietongOnlineEval")
    @ResponseBody
    public Map<String, String> addTietongOnlineEval(HttpServletRequest request, HttpServletResponse response,
                                                    @RequestParam("billMonth") String billMonth,
                                                    @RequestParam("channelEntityName") String channelEntityName,
                                                    @RequestParam("followUpSurveyScore") Double followUpSurveyScore,
                                                    @RequestParam("onlineSystemScore") Double onlineSystemScore,
                                                    @RequestParam("headcount") Integer headcount,
                                                    @RequestParam("scoreFactor") Double scoreFactor,
                                                    @RequestParam("surveyCount") Integer surveyCount,
                                                    @RequestParam(value = "fileName", required = false) String fileName,
                                                    @RequestParam(value = "filePath", required = false) String filePath
    ) {
        Map<String, String> resultMap = new HashMap<String, String>();
		
		SafetyFlewUtils.checkAttributeAndThrow(billMonth);
        SafetyFlewUtils.checkAttributeAndThrow(channelEntityName);
        SafetyFlewUtils.checkAttributeAndThrow(followUpSurveyScore);
        SafetyFlewUtils.checkAttributeAndThrow(onlineSystemScore);
        SafetyFlewUtils.checkAttributeAndThrow(headcount);
        SafetyFlewUtils.checkAttributeAndThrow(scoreFactor);
        SafetyFlewUtils.checkAttributeAndThrow(surveyCount);
        
        String decodedChannelEntityName;
        try {
			decodedChannelEntityName = URLDecoder.decode(URLDecoder.decode(channelEntityName, "UTF-8"), "UTF-8");
		} catch (UnsupportedEncodingException e) {
            return null;
		}
        
        TietongOnlineEval tietongOnlineEval = new TietongOnlineEval();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if (sPrivData != null) {
                tietongOnlineEval.setOrgId(sPrivData.getOrgId());
                tietongOnlineEval.setOpId(sPrivData.getOpId());
            } else {
                resultMap.put("status", "0");
                resultMap.put("msg", "登录超时，请重新登录！");
                return resultMap;
            }
            
            tietongOnlineEval.setBillMonth(billMonth);
            tietongOnlineEval.setChannelEntityName(decodedChannelEntityName);
            tietongOnlineEval.setFollowUpSurveyScore(followUpSurveyScore);
            tietongOnlineEval.setOnlineSystemScore(onlineSystemScore);
            tietongOnlineEval.setHeadcount(headcount);
            tietongOnlineEval.setScoreFactor(scoreFactor);
            tietongOnlineEval.setSurveyCount(surveyCount);
            if (fileName != null && filePath != null) {
                String decodedFileName;
                try {
                    decodedFileName = URLDecoder.decode(URLDecoder.decode(fileName, "UTF-8"), "UTF-8");
                } catch (UnsupportedEncodingException e) {
                    return null;
                }
                tietongOnlineEval.setFileName(decodedFileName);
                tietongOnlineEval.setFilePath(filePath);
            }
            
            agentServiceFeeService.addTietongOnlineEval(tietongOnlineEval);
            resultMap.put("status", "0");
            resultMap.put("message", "录入成功！");
        } catch (Exception e) {
            resultMap.put("status", "-1");
            resultMap.put("message", ExceptionUtil.buildMessage(e));
            logger.error("新增中移铁通在线导购项目考核打分失败，失败原因是：：", e);
        }
        return resultMap;
    }
    
    // 字节数组转十六进制字符串
    private String bytesToHex(byte[] bytes) {
        StringBuilder hex = new StringBuilder();
        for (byte b : bytes) {
            hex.append(String.format("%02X", b));
        }
        return hex.toString();
    }
    
    /**
     * 中移铁通在线导购项目考核打分录入：上传附件
     */
    @RequestMapping("/tietongEvalFilesUpload")
    @ResponseBody
    public Map<String, Object> tietongEvalFilesUpload(HttpServletRequest request, HttpServletResponse response,
                                                      @RequestParam("billMonth") String billMonth,
                                                      @RequestParam("addFile") MultipartFile[] multipartFileArray,
                                                      @RequestParam("editFile") MultipartFile[] multipartFileArray2
    ) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        
        if (multipartFileArray.length == 0) {
            // add和edit只有一个有值，没有add文件就处理edit的文件
            multipartFileArray = multipartFileArray2;
        }
        
        if (multipartFileArray.length == 0) {
            resultMap.put("status", "-1");
            resultMap.put("message", "程序未能获取到上传文件！");
            return resultMap;
        } else if (multipartFileArray.length > 1) {
            resultMap.put("status", "-1");
            resultMap.put("message", "不能上传超过1个文件！");
            return resultMap;
        }
        
        // 检查文件扩展名
        MultipartFile file = multipartFileArray[0];
        String fileName = file.getOriginalFilename();
        if (fileName == null || !(fileName.toLowerCase().endsWith(".xls") || fileName.toLowerCase().endsWith(".doc"))) {
            resultMap.put("status", "-1");
            resultMap.put("message", "文件类型不正确，只允许上传 doc 和 xls 文件！");
            return resultMap;
        }
        SafetyFlewUtils.checkAttributeAndThrow(fileName);
        
        try {
            // 检查文件头
            byte[] fileHeader = new byte[8];
            file.getInputStream().read(fileHeader);
            String fileHeaderHex = bytesToHex(fileHeader);
            
            // doc文件的文件头是D0 CF 11 E0 A1 B1 1A E1
            // xls文件的文件头是D0 CF 11 E0 A1 B1 1A E1
            // xlsx文件的文件头是50 4B 03 04
            // pdf文件的文件头是25 50 44 46
            if (!fileHeaderHex.startsWith("D0CF11E0A1B11AE1")) {
                resultMap.put("status", "-1");
                resultMap.put("message", "文件内容不正确，只允许上传 doc 和 xls 文件！");
                return resultMap;
            }
        } catch (IOException e) {
            resultMap.put("status", "-1");
            resultMap.put("message", "文件读取失败！");
            return resultMap;
        }
        

        try {
            for (MultipartFile multipartFile : multipartFileArray) {
				if (multipartFile.isEmpty()) {
                    continue;
				}
				//服务器IP
				List<ChannelSysBaseType> channelSysBaseTypeList =
						ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 0, null, null);
				String ip = channelSysBaseTypeList.get(0).getCodeName();
				//服务器用户名
				channelSysBaseTypeList =
						ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 1, null, null);
				String username = channelSysBaseTypeList.get(0).getCodeName();
				//服务器密码
				channelSysBaseTypeList =
						ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 2, null, null);
				String password = channelSysBaseTypeList.get(0).getCodeName();
				//服务器文件路径
				channelSysBaseTypeList =
						ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 126, null, null);
				String remotePath = channelSysBaseTypeList.get(0).getCodeName();
				//服务器文件
				String filePath = remotePath + billMonth + "_" + UUID.randomUUID();
				SecureFileTransferProtocol.upload(ip, username, password, multipartFile.getInputStream(), filePath);
				resultMap.put("fileName", multipartFile.getOriginalFilename());
				resultMap.put("filePath", filePath);
			}
            resultMap.put("status", "0");
            resultMap.put("message", "文件上传成功！");
        } catch (Exception e) {
            resultMap.put("status", "-1");
            resultMap.put("message", ExceptionUtil.buildMessage(e));
            logger.error("文件上传失败！", e);
        }
        return resultMap;
    }
    
    @RequestMapping("/editTietongOnlineEval")
    @ResponseBody
    public Map<String, String> editTietongOnlineEval(HttpServletRequest request, HttpServletResponse response,
                                                     @RequestParam("id") Long id,
                                                     @RequestParam(value = "billMonth", required = false) String billMonth,
                                                     @RequestParam(value = "channelEntityName", required = false) String channelEntityName,
                                                     @RequestParam(value = "followUpSurveyScore", required = false) Double followUpSurveyScore,
                                                     @RequestParam(value = "onlineSystemScore", required = false) Double onlineSystemScore,
                                                     @RequestParam(value = "headcount", required = false) Integer headcount,
                                                     @RequestParam(value = "scoreFactor", required = false) Double scoreFactor,
                                                     @RequestParam(value = "surveyCount", required = false) Integer surveyCount,
                                                     @RequestParam(value = "fileName", required = false) String fileName,
                                                     @RequestParam(value = "filePath", required = false) String filePath
    ) {
        Map<String, String> resultMap = new HashMap<String, String>();
        
        SafetyFlewUtils.checkAttributeAndThrow(billMonth);
        SafetyFlewUtils.checkAttributeAndThrow(channelEntityName);
        SafetyFlewUtils.checkAttributeAndThrow(followUpSurveyScore);
        SafetyFlewUtils.checkAttributeAndThrow(onlineSystemScore);
        SafetyFlewUtils.checkAttributeAndThrow(headcount);
        SafetyFlewUtils.checkAttributeAndThrow(scoreFactor);
        SafetyFlewUtils.checkAttributeAndThrow(surveyCount);
        
        // channelEntityName为空也不会报错
        String decodedChannelEntityName;
        try {
            decodedChannelEntityName = URLDecoder.decode(URLDecoder.decode(channelEntityName, "UTF-8"), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            resultMap.put("status", "-1");
            resultMap.put("message", ExceptionUtil.buildMessage(e));
            logger.error("修改中移铁通在线导购项目考核打分失败，失败原因是：", e);
            return resultMap;
        }
        
        
        TietongOnlineEval tietongOnlineEval = new TietongOnlineEval();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if (sPrivData != null){
                tietongOnlineEval.setOrgId(sPrivData.getOrgId());
                tietongOnlineEval.setOpId(sPrivData.getOpId());
            }else {
                throw new RuntimeException("登录信息不存在！");
            }
            
            tietongOnlineEval.setId(id);
            
            // 以下字段都可能为空，这时候Mybatis就不会更新该字段
            tietongOnlineEval.setBillMonth(billMonth);
            tietongOnlineEval.setChannelEntityName(decodedChannelEntityName);
            tietongOnlineEval.setFollowUpSurveyScore(followUpSurveyScore);
            tietongOnlineEval.setOnlineSystemScore(onlineSystemScore);
            tietongOnlineEval.setHeadcount(headcount);
            tietongOnlineEval.setScoreFactor(scoreFactor);
            tietongOnlineEval.setSurveyCount(surveyCount);
            
            if (fileName != null && filePath != null) {
                String decodedFileName;
                try {
                    decodedFileName = URLDecoder.decode(URLDecoder.decode(fileName, "UTF-8"), "UTF-8");
                } catch (UnsupportedEncodingException e) {
                    return null;
                }
                tietongOnlineEval.setFileName(decodedFileName);
                tietongOnlineEval.setFilePath(filePath);
            }
            agentServiceFeeService.editTietongOnlineEval(tietongOnlineEval);
            resultMap.put("status", "0");
            resultMap.put("message", "修改成功！");
        } catch (Exception e) {
            resultMap.put("status", "-1");
            resultMap.put("message", ExceptionUtil.buildMessage(e));
            logger.error("修改中移铁通在线导购项目考核打分失败，失败原因是：", e);
        }
        return resultMap;
    }
    
    /**
     * 中移铁通在线导购项目考核打分录入：查询数据
     */
    @RequestMapping("/queryTietongOnlineEval")
    @ResponseBody
    public PageData<TietongOnlineEval> queryTietongOnlineEval(HttpServletRequest request, HttpServletResponse response,
                                                              @RequestParam("billMonth") String billMonth,
                                                              @RequestParam("channelEntityName") String channelEntityName,
                                                              PageParameter page) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        PageData<TietongOnlineEval> pageData = new PageData<TietongOnlineEval>(null, 0);
        String decodedChannelEntityName;
        try {
			decodedChannelEntityName = URLDecoder.decode(URLDecoder.decode(channelEntityName, "UTF-8"), "UTF-8");
		} catch (UnsupportedEncodingException e) {
            resultMap.put("status", "-1");
            resultMap.put("message", ExceptionUtil.buildMessage(e));
            logger.error("查询中移铁通在线导购项目考核打分失败，失败原因是：", e);
            return null;
		}
        
        TietongOnlineEval tietongOnlineEval = new TietongOnlineEval();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
			if (sPrivData == null) {
				resultMap.put("status", "-1");
				resultMap.put("message", "登录超时，请重新登录！");
				return null;
			}
			tietongOnlineEval.setOrgId(sPrivData.getOrgId());
			tietongOnlineEval.setBillMonth(billMonth);
            tietongOnlineEval.setChannelEntityName(decodedChannelEntityName);
            pageData = agentServiceFeeService.queryTietongOnlineEval(tietongOnlineEval, page);
            
        } catch (Exception e) {
            logger.error("查询中移铁通在线导购项目考核打分失败！", e);
        }
        
        resultMap.put("status", "0");
        resultMap.put("data", pageData);
        return pageData;
    }


    //属地铁通直销项目费用调整录入
    @RequestMapping(value = "/tieTongSupportFeeAdd")
    @ResponseBody
    public Map<String, String> tieTongSupportFeeAdd(HttpServletRequest request, HttpServletResponse response,
                                                  TieTongSupportFeeAdd tieTongSupportFeeAdd) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if(sPrivData == null){
                reMap.put("status", "sPrivDataError");
                reMap.put("messager", "登录时间过长，请重新登录");
                return reMap;
            }
            tieTongSupportFeeAdd.setOrgId(sPrivData.getOrgId());
            tieTongSupportFeeAdd.setOpId(sPrivData.getOpId());
            tieTongSupportFeeAdd.setUserName(sPrivData.getUserName());

            agentServiceFeeService.addTieTongSupportFee(tieTongSupportFeeAdd,sPrivData);

            reMap.put("status", "0");
            reMap.put("message", "保存成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }

    @RequestMapping(value = "/tieTongSupportFeeManage")
    @ResponseBody
    public PageData<TieTongSupportFeeAdd> tieTongSupportFeeManage(HttpServletRequest request, HttpServletResponse response,
                                                              String begUpLoadDate,String endUpLoadDate,
                                                              PageParameter page) {

        PageData<TieTongSupportFeeAdd> pageData = null;
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if(sPrivData == null){
                throw new Exception("登录时间过长，请重新登录");
            }
            pageData = agentServiceFeeService.ManageTieTongSupportFee(begUpLoadDate,endUpLoadDate, page);
        } catch (Exception e) {
            pageData = new PageData<TieTongSupportFeeAdd>(new ArrayList<TieTongSupportFeeAdd>(), 0);
            logger.error("查询失败", e);
        }
        return pageData;
    }

    /*
     * 第三方行销项目支撑费审批 查询
     */
    @RequestMapping(value = "/tieTongSupportFeeInfoQuery")
    @ResponseBody
    public PageData<TieTongSupportFeeAdd> tieTongSupportFeeInfoQuery(HttpServletRequest request,
                                                                     HttpServletResponse response,
                                                                     @RequestParam("recStatusList") String recStatusList,
                                                                     @RequestParam("supportTime") String supportTime,
                                                                     PageParameter page) throws Exception {
        PageData<TieTongSupportFeeAdd> pageData = null;
        SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
        if (sPrivData == null){
            logger.error("登录信息不存在！");
            throw new Exception("登录信息不存在！");
        }
        
        List<Integer> recStatusList2 = ParseUtil.parseStrIntoNumList(recStatusList);
        try {
            TieTongSupportFeeAdd tieTongSupportFeeAdd = new TieTongSupportFeeAdd();
            tieTongSupportFeeAdd.setRecStatusList(recStatusList2.toArray(new Integer[0]));
            tieTongSupportFeeAdd.setSupportTime(supportTime);
            tieTongSupportFeeAdd.setOrgId(sPrivData.getOrgId());
            tieTongSupportFeeAdd.setUserName(sPrivData.getUserName());
            pageData = agentServiceFeeService.tieTongSupportFeeInfoQuery(tieTongSupportFeeAdd, page);
        } catch (Exception e) {
            pageData = new PageData<TieTongSupportFeeAdd>(new ArrayList<TieTongSupportFeeAdd>(), 0);
            logger.error("查询失败", e);
        }
        return pageData;
    }
    
    @RequestMapping(value = "/busiThirdDependRoleAdd")
    @ResponseBody
    public Map<String, String> busiThirdDependRoleAdd(HttpServletRequest request, HttpServletResponse response,
                                                    BusiThirdDependRole busiThirdDependRole) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if(sPrivData == null){
                reMap.put("status", "sPrivDataError");
                reMap.put("messager", "登录时间过长，请重新登录");
                return reMap;
            }
            agentServiceFeeService.busiThirdDependRoleAdd(busiThirdDependRole);

            reMap.put("status", "0");
            reMap.put("message", "保存成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }
    @RequestMapping(value = "/busiThirdDependRoleManage")
    @ResponseBody
    public PageData<BusiThirdDependRole> busiThirdDependRoleManage(HttpServletRequest request, HttpServletResponse response,
                                                                   @RequestParam("orgId")Long orgId,
                                                              PageParameter page) {

        PageData<BusiThirdDependRole> pageData = null;
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if(sPrivData == null){
                throw new Exception("登录时间过长，请重新登录");
            }
            pageData = agentServiceFeeService.busiThirdDependRoleManage(orgId, page);
        } catch (Exception e) {
            pageData = new PageData<BusiThirdDependRole>(new ArrayList<BusiThirdDependRole>(), 0);
            logger.error("查询失败", e);
        }
        return pageData;
    }
    @RequestMapping(value = "/busiThirdDependRoleEdit")
    @ResponseBody
    public Map<String, String> busiThirdDependRoleEdit(HttpServletRequest request, HttpServletResponse response,
                                              BusiThirdDependRole busiThirdDependRole) {
        Map<String, String> reMap = new HashMap<String, String>();
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if(sPrivData == null){
                throw new Exception("登录时间过长，请重新登录");
            }

            busiThirdDependRole.setDoneDate(new Date());
            agentServiceFeeService.busiThirdDependRoleEdit(busiThirdDependRole);
            reMap.put("status", "0");
            reMap.put("message", "保存成功！");
        } catch (Exception e) {
            reMap.put("status", "1");
            reMap.put("message", e.getMessage());
            logger.error("", e);
        }
        return reMap;
    }

    /**
     * 中高端保有项目人员月度考评录入页面
     */
    @RequestMapping(value = "/page/midHighEndRetentionAssessmentAdd")
    public String midHighEndRetentionAssessmentAdd() {
        return "award/midHighEndRetentionAssessmentAdd";
    }

    /*
     * 中高端保有项目人员月度考评 批量录入
     */
    @RequestMapping(value = "/midHighEndRetentionAssessmentInfoBatchAdd")
    @ResponseBody
    public Map<String, String> midHighEndRetentionAssessmentInfoBatchAdd(HttpServletRequest request, HttpServletResponse response,
                                                                @RequestParam(value = "fileName", required = false) String fileName,
                                                                @RequestParam(value = "flowThreeAmaderName", required = false) String flowThreeAmaderName,
                                                                @RequestParam(value = "remoteFileName", required = false) String remoteFileName,
                                                                @RequestParam(value = "remotePath", required = false) String remotePath,
                                                                @RequestParam MultipartFile[] files) throws Exception{
        return null;
    }

    /**
     * @return
     * <AUTHOR> 默认加载页面   中高端保有项目人员月度考评录入
     */
    @RequestMapping(value = "/page/midHighEndRetentionAssessmentAdd")
    public String midHighEndRetentionAssessmentAdd() {
        return "award/midHighEndRetentionAssessmentAdd";
    }

    /**
     * @return
     * <AUTHOR> 默认加载页面   中高端保有项目人员月度考评管理
     */
    @RequestMapping(value = "/page/midHighEndRetentionAssessmentManage")
    public String midHighEndRetentionAssessmentManage() {
        return "award/midHighEndRetentionAssessmentManage";
    }

    /*
     * 中高端保有项目人员月度考评 错误文件导出
     */
    @RequestMapping(value = "/midHighEndRetentionAssessmentInfoBatchAddResultExport")
    public void midHighEndRetentionAssessmentInfoBatchAddResultExport(
            HttpServletRequest request, HttpServletResponse response) throws Exception{
        // TODO: 实现错误文件导出功能
    }

    /*
     * 中高端保有项目人员月度考评 附件上传
     */
    @RequestMapping("/midHighEndRetentionAssessmentFilesUpload")
    @ResponseBody
    public Map<String, Object> midHighEndRetentionAssessmentFilesUpload(HttpServletRequest request, HttpServletResponse response,
                                                                        @RequestParam(value = "billMonth", required = true) String billMonth,
                                                                        @RequestParam(value = "fileName", required = true) String fileName,
                                                                        @RequestParam MultipartFile[] files) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            for (MultipartFile multipartFile : files) {
                if (!multipartFile.isEmpty()) {
                    //服务器IP
                    List<ChannelSysBaseType> channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 0, null, null);
                    String ip = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器用户名
                    channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 1, null, null);
                    String username = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器密码
                    channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 2, null, null);
                    String password = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器文件路径
                    channelSysBaseTypeList =
                            ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 111, null, null);
                    String remotePath = channelSysBaseTypeList.get(0).getCodeName();
                    //服务器文件
                    String remoteFileName = multipartFile.getOriginalFilename();
                    String remoteFile = remotePath + billMonth + "_" + UUID.randomUUID().toString();
                    SecureFileTransferProtocol.upload(ip, username, password, multipartFile.getInputStream(), remoteFile);
                    resultMap.put("remoteFileName", remoteFileName);
                    resultMap.put("remotePath", remoteFile);
                }
            }
            resultMap.put("status", "1");
            resultMap.put("msg", "文件上传成功！");
        } catch (Exception e) {
            resultMap.put("status", "0");
            resultMap.put("msg", "文件上传失败！");
            logger.error("中高端保有项目人员月度考评文件上传失败！", e);
        }
        return resultMap;
    }
}
