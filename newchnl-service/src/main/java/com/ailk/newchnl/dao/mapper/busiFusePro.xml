<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ailk.newchnl.dao.BusiFuseProDao">
    <insert id="insert" parameterType="map">
        INSERT INTO BUSI_FUSE_PRO (
            bill_month,
            channel_entity_id,
            channel_entity_name,
            org_name,
            done_date,
            op_id,
            org_id,
            rec_status
        )
        VALUES
        (#{billMonth,jdbcType=VARCHAR},
         #{channelEntityId,jdbcType=NUMERIC},
         #{channelEntityName,jdbcType=VARCHAR},
         #{orgName,jdbcType=VARCHAR},
         #{doneDate,jdbcType=TIMESTAMP},
         #{opId,jdbcType=NUMERIC},
         #{orgId,jdbcType=NUMERIC},
         #{recStatus,jdbcType=NUMERIC}
        )
    </insert>


    <select id="query" parameterType="map" resultType="com.ailk.newchnl.entity.BusiFusePro">
        SELECT * FROM BUSI_FUSE_PRO t where rec_status =1
        <choose>
            <when test="entity.billMonth != null and entity.billMonth != '' ">
                AND bill_month = #{entity.billMonth}
            </when>
        </choose>
        <choose>
            <when test="entity.orgId != null and entity.orgId != ''">
                AND org_id = #{entity.orgId}
            </when>
        </choose>
        <choose>
            <when test="entity.channelEntityName != null and entity.channelEntityName != ''">
                AND channel_entity_name LIKE
                CONCAT(CONCAT('%',#{entity.channelEntityName}),'%')
            </when>
        </choose>
    </select>


    <select id="queryBusiFuseProInfo" parameterType="map" resultType="com.ailk.newchnl.entity.BusiFusePro">
        SELECT * FROM BUSI_FUSE_PRO t where rec_status =1
        <choose>
            <when test="entity.billMonth != null and entity.billMonth != '' ">
                AND bill_month = #{entity.billMonth}
            </when>
        </choose>
        <choose>
            <when test="entity.orgId != null and entity.orgId != ''">
                AND org_id = #{entity.orgId}
            </when>
        </choose>
        <choose>
            <when test="entity.channelEntityName != null and entity.channelEntityName != ''">
                AND channel_entity_name LIKE
                CONCAT(CONCAT('%',#{entity.channelEntityName}),'%')
            </when>
        </choose>
    </select>

    <update id="update" parameterType="map">
        UPDATE BUSI_FUSE_PRO
        <set>
            <if test="recStatus != null">rec_status = #{recStatus,jdbcType=NUMERIC}, </if>
            <if test="doneDate != null">done_date = #{doneDate,jdbcType=TIMESTAMP},</if>
        </set>
        WHERE  rec_status = 1
                <choose>
                    <when test="channelEntityName != null">
                        AND channel_entity_name = #{channelEntityName}
                    </when>
                </choose>
                and bill_month = #{billMonth}
    </update>

    <select id="queryNodeByName" resultType="map">
        select a.channel_entity_id, a.channel_entity_name
        from channel_entity_basic_info a, channel_node b
        where a.channel_entity_id = b.node_id
        and a.rec_status = 1
        and b.rec_status = 1
        and a.channel_entity_status not in(5,12)
        and a.channel_entity_type=2
        <choose>
            <when test="channelEntityName != null">
                AND a.channel_entity_name = #{channelEntityName}
            </when>
        </choose>
    </select>

    <select id="queryNodeAndMonth" resultType="map">
        select *
        from BUSI_FUSE_PRO t
        where  t.rec_status=1
        AND   t.bill_month = #{billMonth}
        AND t.channel_Entity_Name = #{channelEntityName}
    </select>

</mapper>