/**
 * $Id: busiFuseProDao.java,v 1.0 2023/12/29 14:43 asus Exp $
 * <p>
 * Copyright 2016 Asiainfo Technologies(China),Inc. All rights reserved.
 */
package com.ailk.newchnl.dao;

import com.ailk.newchnl.dao.impl.BaseDao;
import com.ailk.newchnl.entity.BusiFusePro;
import com.ailk.newchnl.mybatis.pagination.PageParameter;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $Id: busiFuseProDao.java,v 1.1 2023/12/29 14:43 asus Exp $
 * Created on 2023/12/29 14:43
 */
@Repository("BusiFuseProDao")
public interface BusiFuseProDao extends BaseDao {
    public List<BusiFusePro> queryBusiFuseProInfo(
            @Param(value = "entity") BusiFusePro busiFusePro,
            @Param(value = "page") PageParameter page);
    public List<Map<String, Object>> queryNodeByName(@Param(value = "channelEntityName") String channelEntityName);

    public List<Map<String, Object>> queryNodeAndMonth(@Param(value = "channelEntityName") String channelEntityName,
                                                       @Param(value = "billMonth") String billMonth);
}