<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ailk.newchnl.dao.MidHighEndRetentionAssessmentDao">

    <!-- 获取序列号 -->
    <select id="getSequence" resultType="long">
        SELECT MID_HIGH_END_RETENTION_ASSESSMENT_SEQ.NEXTVAL FROM DUAL
    </select>

    <!-- 分页查询 -->
    <select id="pageQuery" parameterType="map" resultType="com.ailk.newchnl.entity.MidHighEndRetentionAssessment">
        SELECT * FROM mid_high_end_retention_assessment
        WHERE 1=1
        <if test="entity.billMonth != null and entity.billMonth != ''">
            AND bill_month = #{entity.billMonth}
        </if>
        <if test="entity.orgId != null">
            AND org_id = #{entity.orgId}
        </if>
        <if test="entity.recStatus != null and entity.recStatus != ''">
            AND rec_status = #{entity.recStatus}
        </if>
        <if test="entity.recStatusList != null and entity.recStatusList != ''">
            AND rec_status IN
            <foreach collection="entity.recStatusList.split(',')" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        ORDER BY done_date DESC
    </select>

    <!-- 插入 -->
    <insert id="insert" parameterType="com.ailk.newchnl.entity.MidHighEndRetentionAssessment">
        INSERT INTO mid_high_end_retention_assessment (
            done_code, bill_month, role_name, ebc_job_number, assessment_coefficient,
            basic_cost_adjust_fee, adjust_fee, org_id, org_name, op_id, user_name,
            done_date, rec_status, agent_adjust_use_id, rec_status_approve,
            file_name, file_path
        ) VALUES (
            #{doneCode}, #{billMonth}, #{roleName}, #{ebcJobNumber}, #{assessmentCoefficient},
            #{basicCostAdjustFee}, #{adjustFee}, #{orgId}, #{orgName}, #{opId}, #{userName},
            #{doneDate}, #{recStatus}, #{agentAdjustUseId}, #{recStatusApprove},
            #{fileName}, #{filePath}
        )
    </insert>

    <!-- 更新 -->
    <update id="update" parameterType="com.ailk.newchnl.entity.MidHighEndRetentionAssessment">
        UPDATE mid_high_end_retention_assessment
        <set>
            <if test="roleName != null">role_name = #{roleName},</if>
            <if test="ebcJobNumber != null">ebc_job_number = #{ebcJobNumber},</if>
            <if test="assessmentCoefficient != null">assessment_coefficient = #{assessmentCoefficient},</if>
            <if test="basicCostAdjustFee != null">basic_cost_adjust_fee = #{basicCostAdjustFee},</if>
            <if test="adjustFee != null">adjust_fee = #{adjustFee},</if>
            <if test="recStatus != null">rec_status = #{recStatus},</if>
            <if test="recStatusApprove != null">rec_status_approve = #{recStatusApprove},</if>
            <if test="agentAdjustUseId != null">agent_adjust_use_id = #{agentAdjustUseId},</if>
            <if test="opId != null">op_id = #{opId},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="doneDate != null">done_date = #{doneDate},</if>
        </set>
        WHERE done_code = #{doneCode}
    </update>

    <!-- 根据条件删除 -->
    <delete id="deleteByCondition" parameterType="com.ailk.newchnl.entity.MidHighEndRetentionAssessment">
        DELETE FROM mid_high_end_retention_assessment
        WHERE 1=1
        <if test="billMonth != null and billMonth != ''">
            AND bill_month = #{billMonth}
        </if>
        <if test="orgId != null">
            AND org_id = #{orgId}
        </if>
    </delete>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO mid_high_end_retention_assessment (
            done_code, bill_month, role_name, ebc_job_number, assessment_coefficient,
            basic_cost_adjust_fee, adjust_fee, org_id, org_name, op_id, user_name,
            done_date, rec_status, agent_adjust_use_id, rec_status_approve,
            file_name, file_path
        )
        <foreach collection="list" item="item" separator="UNION ALL">
            SELECT
            #{item.doneCode}, #{item.billMonth}, #{item.roleName}, #{item.ebcJobNumber}, #{item.assessmentCoefficient},
            #{item.basicCostAdjustFee}, #{item.adjustFee}, #{item.orgId}, #{item.orgName}, #{item.opId}, #{item.userName},
            #{item.doneDate}, #{item.recStatus}, #{item.agentAdjustUseId}, #{item.recStatusApprove},
            #{item.fileName}, #{item.filePath}
            FROM DUAL
        </foreach>
    </insert>

    <!-- 根据ID查询 -->
    <select id="queryById" parameterType="long" resultType="com.ailk.newchnl.entity.MidHighEndRetentionAssessment">
        SELECT * FROM mid_high_end_retention_assessment WHERE done_code = #{doneCode}
    </select>

    <!-- 普通查询 -->
    <select id="query" parameterType="com.ailk.newchnl.entity.MidHighEndRetentionAssessment" resultType="com.ailk.newchnl.entity.MidHighEndRetentionAssessment">
        SELECT * FROM mid_high_end_retention_assessment
        WHERE 1=1
        <if test="billMonth != null and billMonth != ''">
            AND bill_month = #{billMonth}
        </if>
        <if test="orgId != null">
            AND org_id = #{orgId}
        </if>
        <if test="recStatus != null and recStatus != ''">
            AND rec_status = #{recStatus}
        </if>
        <if test="doneCode != null">
            AND done_code = #{doneCode}
        </if>
        ORDER BY done_date DESC
    </select>

</mapper>