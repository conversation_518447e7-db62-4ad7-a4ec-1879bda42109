package com.ailk.newchnl.dao;

import com.ailk.newchnl.dao.impl.BaseDao;
import com.ailk.newchnl.entity.MidHighEndRetentionAssessment;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 中高端保有项目人员月度考评DAO接口
 * <AUTHOR> @version $Id: MidHighEndRetentionAssessmentDao.java,v 1.0 2025/09/04 $
 * Created on 2025/09/04
 */
@Repository("midHighEndRetentionAssessmentDao")
public interface MidHighEndRetentionAssessmentDao extends BaseDao<MidHighEndRetentionAssessment> {

    /**
     * 获取序列号
     */
    Long getSequence();

    /**
     * 批量插入
     */
    void batchInsert(List<MidHighEndRetentionAssessment> assessments);

    /**
     * 根据条件删除
     */
    void deleteByCondition(MidHighEndRetentionAssessment assessment);

}