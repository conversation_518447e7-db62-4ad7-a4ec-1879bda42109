<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" 
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd ">
  <modelVersion>4.0.0</modelVersion>
  <groupId>junit</groupId>
  <artifactId>junit</artifactId>
  <version>3.8.1</version>
  <name>JUnit</name>
  <url>http://junit.org</url>
  <description>
    JUnit is a regression testing framework written by <PERSON> and <PERSON>. It is used by the developer who implements unit tests in Java.
  </description>
  <organization>
    <name>JUnit</name>
    <url>http://www.junit.org</url>
  </organization>
  <licenses>
    <license>
      <name>Common Public License Version 1.0</name>
      <url>http://www.opensource.org/licenses/cpl1.0.txt</url>
    </license>
  </licenses>
  <scm>
    <url>http://junit.cvs.sourceforge.net/junit/</url>
  </scm>
  <dependencies>
  </dependencies>
</project>
