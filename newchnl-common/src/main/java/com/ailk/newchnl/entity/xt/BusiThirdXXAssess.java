/**
 * $Id: BusiThirdXXAssessAdd.java,v 1.0 2023/11/9 14:51 asus Exp $
 * <p>
 * Copyright 2016 Asiainfo Technologies(China),Inc. All rights reserved.
 */
package com.ailk.newchnl.entity.xt;

import com.ailk.newchnl.util.ChannelSysBaseTypeAnnotation;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version $Id: BusiThirdXXAssessAdd.java,v 1.1 2023/11/9 14:51 asus Exp $
 * Created on 2023/11/9 14:51
 */
public class BusiThirdXXAssess implements Serializable {
    private Long doneCode;                  //序列号
    private String billMonth;               //录入月份
    private String channelTeam;             //团队
    private Long channelEntityId;           //网点ID
    private String channelEntityName;       //网点名称
    private Integer assesmentScore;          //行销人员考核得分
    private Integer assesTTScore;            //行销人员铁通考核得分
    private Integer negativePenaltyScore;    //负向扣罚得分
    private Long orgId;                     //归属组织
    private String orgName;                 //所属分公司 50445
    private Long opId;                      //操作员Id
    private String userName;                //操作员工号
    private Date doneDate;                  //操作时间
    private Integer recStatus;              //状态 0：待审核  1：审核通过  2：审核驳回   3：-1  失效
    private String recStatusList;          // 用来存放查询条件的字段，数据库中没有
    private Long agentAdjustUseId;          //审批人Id
    private String recStatusApprove;        //审批状态
    private String fileName;                //附件名称
    private String filePath;                //附件下载路径
    @ChannelSysBaseTypeAnnotation(codeType = 50468)
    private Long roleName;          //角色

    private Double billMonthScore;           //当月考核得分
    private Double assesGridScore;          //其中：网格长考核打分
    private Double basicCostFee;            //基本费用分配
    private Double marketAdjustFee;          //营销费用调整费
    private String billDate;                //操作时间 字符
    
    private String agentAdjustUseName; // 下一次审批要找谁
    
    public String getAgentAdjustUseName() {
        return agentAdjustUseName;
    }
    
    public void setAgentAdjustUseName(String agentAdjustUseName) {
        this.agentAdjustUseName = agentAdjustUseName;
    }
    
    
    public String getRecStatusList() {
        return recStatusList;
    }
    
    public void setRecStatusList(String recStatusList) {
        this.recStatusList = recStatusList;
    }
    
    public String getBillMonth() {
        return billMonth;
    }

    public void setBillMonth(String billMonth) {
        this.billMonth = billMonth;
    }

    public String getChannelTeam() {
        return channelTeam;
    }

    public void setChannelTeam(String channelTeam) {
        this.channelTeam = channelTeam;
    }

    public Long getChannelEntityId() {
        return channelEntityId;
    }

    public void setChannelEntityId(Long channelEntityId) {
        this.channelEntityId = channelEntityId;
    }

    public String getChannelEntityName() {
        return channelEntityName;
    }

    public void setChannelEntityName(String channelEntityName) {
        this.channelEntityName = channelEntityName;
    }

    public Integer getAssesmentScore() {
        return assesmentScore;
    }

    public void setAssesmentScore(Integer assesmentScore) {
        this.assesmentScore = assesmentScore;
    }

    public Integer getAssesTTScore() {
        return assesTTScore;
    }

    public void setAssesTTScore(Integer assesTTScore) {
        this.assesTTScore = assesTTScore;
    }

    public Integer getNegativePenaltyScore() {
        return negativePenaltyScore;
    }

    public void setNegativePenaltyScore(Integer negativePenaltyScore) {
        this.negativePenaltyScore = negativePenaltyScore;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Long getOpId() {
        return opId;
    }

    public void setOpId(Long opId) {
        this.opId = opId;
    }

    public Date getDoneDate() {
        return doneDate;
    }

    public void setDoneDate(Date doneDate) {
        this.doneDate = doneDate;
    }

    public Integer getRecStatus() {
        return recStatus;
    }

    public void setRecStatus(Integer recStatus) {
        this.recStatus = recStatus;
    }

    public Long getDoneCode() {
        return doneCode;
    }

    public void setDoneCode(Long doneCode) {
        this.doneCode = doneCode;
    }

    public Long getAgentAdjustUseId() {
        return agentAdjustUseId;
    }

    public void setAgentAdjustUseId(Long agentAdjustUseId) {
        this.agentAdjustUseId = agentAdjustUseId;
    }

    public String getRecStatusApprove() {
        return recStatusApprove;
    }

    public void setRecStatusApprove(String recStatusApprove) {
        this.recStatusApprove = recStatusApprove;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public Long getRoleName() {
        return roleName;
    }

    public void setRoleName(Long roleName) {
        this.roleName = roleName;
    }

    public Double getBillMonthScore() {
        return billMonthScore;
    }

    public void setBillMonthScore(Double billMonthScore) {
        this.billMonthScore = billMonthScore;
    }

    public Double getAssesGridScore() {
        return assesGridScore;
    }

    public void setAssesGridScore(Double assesGridScore) {
        this.assesGridScore = assesGridScore;
    }

    public Double getBasicCostFee() {
        return basicCostFee;
    }

    public void setBasicCostFee(Double basicCostFee) {
        this.basicCostFee = basicCostFee;
    }

    public Double getMarketAdjustFee() {
        return marketAdjustFee;
    }

    public void setMarketAdjustFee(Double marketAdjustFee) {
        this.marketAdjustFee = marketAdjustFee;
    }

    public String getBillDate() {
        return billDate;
    }

    public void setBillDate(String billDate) {
        this.billDate = billDate;
    }
}
